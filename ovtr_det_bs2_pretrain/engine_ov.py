# Copyright (c) Jin<PERSON> Li. All Rights Reserved.
# ------------------------------------------------------------------------
# Modified from OV DETR
# Copyright (c) S-LAB, Nanyang Technological University. All Rights Reserved.
# ------------------------------------------------------------------------

import math
import sys
from typing import Iterable
import torch
from torch.cuda.amp import GradScaler, autocast

import util.misc as utils
from datasets.data_prefetcher import data_prefetcher
from util.list_LVIS import novel_class

def data_novel_filter(targets, novel_cls):
    targets_norare=[]
    for target in targets:
        novel_index = torch.isin(target['labels'], novel_cls)
        if len(target['labels'][novel_index])==0:
            targets_norare.append(target)
            continue
        else:
            target['labels'] = target['labels'][~novel_index]
            target['boxes'] = target['boxes'][~novel_index]
            target['area'] = target['area'][~novel_index]
            target['iscrowd'] = target['iscrowd'][~novel_index]
            targets_norare.append(target)
    return targets_norare

def train_one_epoch(
    model: torch.nn.Module,
    criterion: torch.nn.Module,
    data_loader: Iterable,
    optimizer: torch.optim.Optimizer,
    device: torch.device,
    epoch: int,
    max_norm: float = 0,
    masks: bool = False,
    amp: bool = False,
):
    model.train()
    criterion.train()
    metric_logger = utils.MetricLogger(delimiter="  ")
    metric_logger.add_meter("lr", utils.SmoothedValue(window_size=1, fmt="{value:.6f}"))
    metric_logger.add_meter("grad_norm", utils.SmoothedValue(window_size=1, fmt="{value:.2f}"))
    header = "Epoch: [{}]".format(epoch)
    print_freq = 10

    scaler = GradScaler()

    prefetcher = data_prefetcher(data_loader, device, prefetch=True)
    samples, targets = prefetcher.next()
    # novel cls filter
    novel_cls = novel_class.to(samples.mask.device)
    targets = data_novel_filter(targets, novel_cls)

    for _ in metric_logger.log_every(range(len(data_loader)), print_freq, header):
        # novel cls filter
        while sum([len(target['labels']) for target in targets]) == 0:
            samples, targets = prefetcher.next()
            if not targets: 
                break
            targets = data_novel_filter(targets, novel_cls)
        if not targets: 
            break

        # start
        with autocast(enabled=amp):
            if not masks:
                outputs = model(samples, targets)
            else:
                outputs = model(samples, targets, criterion)
            loss_dict = criterion(outputs, targets)
        weight_dict = criterion.weight_dict
        losses = sum(loss_dict[k] * weight_dict[k] for k in loss_dict.keys() if k in weight_dict)

        # reduce losses over all GPUs for logging purposes
        loss_dict_reduced = utils.reduce_dict(loss_dict)
        loss_dict_reduced_unscaled = {f"{k}_unscaled": v for k, v in loss_dict_reduced.items()}
        loss_dict_reduced_scaled = {k: v * weight_dict[k]
                                     for k, v in loss_dict_reduced.items() if k in weight_dict}
        losses_reduced_scaled = sum(loss_dict_reduced_scaled.values())

        loss_value = losses_reduced_scaled.item()

        if not math.isfinite(loss_value):
            print("Loss is {}, stopping training".format(loss_value))
            print(loss_dict_reduced)
            sys.exit(1)

        if amp:
            optimizer.zero_grad()
            scaler.scale(losses).backward()
            if max_norm > 0:
                scaler.unscale_(optimizer)
                grad_total_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm)
            else:
                grad_total_norm = utils.get_total_grad_norm(model.parameters(), max_norm)
            scaler.step(optimizer)
            scaler.update()
        else:
            optimizer.zero_grad()
            losses.backward()
            if max_norm > 0:
                grad_total_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm)
            else:
                grad_total_norm = utils.get_total_grad_norm(model.parameters(), max_norm)
            optimizer.step()

        metric_logger.update(
            loss=loss_value, **loss_dict_reduced_scaled, **loss_dict_reduced_unscaled
        )
        metric_logger.update(lr=optimizer.param_groups[0]["lr"])
        metric_logger.update(grad_norm=grad_total_norm)

        samples, targets = prefetcher.next()
        
        if not targets: 
            break
        targets = data_novel_filter(targets, novel_cls)

    # gather the stats from all processes
    metric_logger.synchronize_between_processes()
    print("Averaged stats:", metric_logger)
    return {k: meter.global_avg for k, meter in metric_logger.meters.items()}