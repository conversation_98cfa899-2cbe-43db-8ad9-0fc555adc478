#!/usr/bin/env python3
import cv2
import sys

def test_video_backends(video_path):
    """测试不同的OpenCV后端来打开视频"""
    print(f"测试视频文件: {video_path}")
    
    # 测试默认后端
    print("\n1. 测试默认后端...")
    cap = cv2.VideoCapture(video_path)
    if cap.isOpened():
        print("✓ 默认后端成功")
        print_video_info(cap)
        cap.release()
        return True
    else:
        print("✗ 默认后端失败")
        cap.release()
    
    # 测试其他后端
    backends = [
        (cv2.CAP_FFMPEG, "FFMPEG"),
        (cv2.CAP_GSTREAMER, "GSTREAMER"),
    ]
    
    # 添加V4L2后端（如果可用）
    if hasattr(cv2, 'CAP_V4L2'):
        backends.append((cv2.CAP_V4L2, "V4L2"))
    
    for backend_id, backend_name in backends:
        print(f"\n2. 测试 {backend_name} 后端...")
        try:
            cap = cv2.VideoCapture(video_path, backend_id)
            if cap.isOpened():
                print(f"✓ {backend_name} 后端成功")
                print_video_info(cap)
                cap.release()
                return True
            else:
                print(f"✗ {backend_name} 后端失败")
                cap.release()
        except Exception as e:
            print(f"✗ {backend_name} 后端异常: {e}")
    
    return False

def print_video_info(cap):
    """打印视频信息"""
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    fourcc = int(cap.get(cv2.CAP_PROP_FOURCC))
    
    # 将fourcc转换为字符串
    fourcc_str = "".join([chr((fourcc >> 8 * i) & 0xFF) for i in range(4)])
    
    print(f"  分辨率: {width}x{height}")
    print(f"  帧率: {fps:.2f} fps")
    print(f"  总帧数: {frame_count}")
    print(f"  编码格式: {fourcc_str} ({fourcc})")

def test_video_codecs():
    """测试可用的视频编解码器"""
    print("\n测试可用的视频编解码器:")
    
    codecs_to_test = [
        'avc1',  # H.264
        'H264',  # H.264 alternative
        'mp4v',  # MPEG-4
        'XVID',  # XVID
        'MJPG',  # Motion JPEG
        'X264',  # x264
    ]
    
    for codec in codecs_to_test:
        try:
            fourcc = cv2.VideoWriter_fourcc(*codec)
            # 尝试创建一个临时的VideoWriter来测试编解码器
            writer = cv2.VideoWriter('/tmp/test.mp4', fourcc, 30, (640, 480))
            if writer.isOpened():
                print(f"✓ {codec} 编解码器可用")
                writer.release()
            else:
                print(f"✗ {codec} 编解码器不可用")
                writer.release()
        except Exception as e:
            print(f"✗ {codec} 编解码器异常: {e}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python test_video.py <video_path>")
        print("示例: python test_video.py /path/to/your/video.mp4")
        sys.exit(1)
    
    video_path = sys.argv[1]
    
    print("OpenCV版本:", cv2.__version__)
    
    # 测试视频读取
    success = test_video_backends(video_path)
    
    # 测试编解码器
    test_video_codecs()
    
    if success:
        print("\n✓ 视频文件可以正常读取")
    else:
        print("\n✗ 无法读取视频文件")
        sys.exit(1)
