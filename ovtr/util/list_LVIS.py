import torch

CLASSES_dict = {1: 'aerosol_can', 2: 'air_conditioner', 3: 'airplane', 4: 'alarm_clock', 5: 'alcohol', 6: 'alligator', 7: 'almond', 8: 'ambulance', 9: 'amplifier', 10: 'anklet', 11: 'antenna', 12: 'apple', 13: 'applesauce', 14: 'apricot', 15: 'apron', 16: 'aquarium', 17: 'arctic_(type_of_shoe)', 18: 'armband', 19: 'armchair', 20: 'armoire', 21: 'armor', 22: 'artichoke', 23: 'trash_can', 24: 'ashtray', 25: 'asparagus', 26: 'atomizer', 27: 'avocado', 28: 'award', 29: 'awning', 30: 'ax', 31: 'baboon', 32: 'baby_buggy', 33: 'basketball_backboard', 34: 'backpack', 35: 'handbag', 36: 'suitcase', 37: 'bagel', 38: 'bagpipe', 39: 'baguet', 40: 'bait', 41: 'ball', 42: 'ballet_skirt', 43: 'balloon', 44: 'bamboo', 45: 'banana', 46: 'Band_Aid', 47: 'bandage', 48: 'bandanna', 49: 'banjo', 50: 'banner', 51: 'barbell', 52: 'barge', 53: 'barrel', 54: 'barrette', 55: 'barrow', 56: 'baseball_base', 57: 'baseball', 58: 'baseball_bat', 59: 'baseball_cap', 60: 'baseball_glove', 61: 'basket', 62: 'basketball', 63: 'bass_horn', 64: 'bat_(animal)', 65: 'bath_mat', 66: 'bath_towel', 67: 'bathrobe', 68: 'bathtub', 69: 'batter_(food)', 70: 'battery', 71: 'beachball', 72: 'bead', 73: 'bean_curd', 74: 'beanbag', 75: 'beanie', 76: 'bear', 77: 'bed', 78: 'bedpan', 79: 'bedspread', 80: 'cow', 81: 'beef_(food)', 82: 'beeper', 83: 'beer_bottle', 84: 'beer_can', 85: 'beetle', 86: 'bell', 87: 'bell_pepper', 88: 'belt', 89: 'belt_buckle', 90: 'bench', 91: 'beret', 92: 'bib', 93: 'Bible', 94: 'bicycle', 95: 'visor', 96: 'billboard', 97: 'binder', 98: 'binoculars', 99: 'bird', 100: 'birdfeeder', 101: 'birdbath', 102: 'birdcage', 103: 'birdhouse', 104: 'birthday_cake', 105: 'birthday_card', 106: 'pirate_flag', 107: 'black_sheep', 108: 'blackberry', 109: 'blackboard', 110: 'blanket', 111: 'blazer', 112: 'blender', 113: 'blimp', 114: 'blinker', 115: 'blouse', 116: 'blueberry', 117: 'gameboard', 118: 'boat', 119: 'bob', 120: 'bobbin', 121: 'bobby_pin', 122: 'boiled_egg', 123: 'bolo_tie', 124: 'deadbolt', 125: 'bolt', 126: 'bonnet', 127: 'book', 128: 'bookcase', 129: 'booklet', 130: 'bookmark', 131: 'boom_microphone', 132: 'boot', 133: 'bottle', 134: 'bottle_opener', 135: 'bouquet', 136: 'bow_(weapon)', 137: 'bow_(decorative_ribbons)', 138: 'bow-tie', 139: 'bowl', 140: 'pipe_bowl', 141: 'bowler_hat', 142: 'bowling_ball', 143: 'box', 144: 'boxing_glove', 145: 'suspenders', 146: 'bracelet', 147: 'brass_plaque', 148: 'brassiere', 149: 'bread-bin', 150: 'bread', 151: 'breechcloth', 152: 'bridal_gown', 153: 'briefcase', 154: 'broccoli', 155: 'broach', 156: 'broom', 157: 'brownie', 158: 'brussels_sprouts', 159: 'bubble_gum', 160: 'bucket', 161: 'horse_buggy', 162: 'bull', 163: 'bulldog', 164: 'bulldozer', 165: 'bullet_train', 166: 'bulletin_board', 167: 'bulletproof_vest', 168: 'bullhorn', 169: 'bun', 170: 'bunk_bed', 171: 'buoy', 172: 'burrito', 173: 'bus_(vehicle)', 174: 'business_card', 175: 'butter', 176: 'butterfly', 177: 'button', 178: 'cab_(taxi)', 179: 'cabana', 180: 'cabin_car', 181: 'cabinet', 182: 'locker', 183: 'cake', 184: 'calculator', 185: 'calendar', 186: 'calf', 187: 'camcorder', 188: 'camel', 189: 'camera', 190: 'camera_lens', 191: 'camper_(vehicle)', 192: 'can', 193: 'can_opener', 194: 'candle', 195: 'candle_holder', 196: 'candy_bar', 197: 'candy_cane', 198: 'walking_cane', 199: 'canister', 200: 'canoe', 201: 'cantaloup', 202: 'canteen', 203: 'cap_(headwear)', 204: 'bottle_cap', 205: 'cape', 206: 'cappuccino', 207: 'car_(automobile)', 208: 'railcar_(part_of_a_train)', 209: 'elevator_car', 210: 'car_battery', 211: 'identity_card', 212: 'card', 213: 'cardigan', 214: 'cargo_ship', 215: 'carnation', 216: 'horse_carriage', 217: 'carrot', 218: 'tote_bag', 219: 'cart', 220: 'carton', 221: 'cash_register', 222: 'casserole', 223: 'cassette', 224: 'cast', 225: 'cat', 226: 'cauliflower', 227: 'cayenne_(spice)', 228: 'CD_player', 229: 'celery', 230: 'cellular_telephone', 231: 'chain_mail', 232: 'chair', 233: 'chaise_longue', 234: 'chalice', 235: 'chandelier', 236: 'chap', 237: 'checkbook', 238: 'checkerboard', 239: 'cherry', 240: 'chessboard', 241: 'chicken_(animal)', 242: 'chickpea', 243: 'chili_(vegetable)', 244: 'chime', 245: 'chinaware', 246: 'crisp_(potato_chip)', 247: 'poker_chip', 248: 'chocolate_bar', 249: 'chocolate_cake', 250: 'chocolate_milk', 251: 'chocolate_mousse', 252: 'choker', 253: 'chopping_board', 254: 'chopstick', 255: 'Christmas_tree', 256: 'slide', 257: 'cider', 258: 'cigar_box', 259: 'cigarette', 260: 'cigarette_case', 261: 'cistern', 262: 'clarinet', 263: 'clasp', 264: 'cleansing_agent', 265: 'cleat_(for_securing_rope)',
                266: 'clementine', 267: 'clip', 268: 'clipboard', 269: 'clippers_(for_plants)', 270: 'cloak', 271: 'clock', 272: 'clock_tower', 273: 'clothes_hamper', 274: 'clothespin', 275: 'clutch_bag', 276: 'coaster', 277: 'coat', 278: 'coat_hanger', 279: 'coatrack', 280: 'cock', 281: 'cockroach', 282: 'cocoa_(beverage)', 283: 'coconut', 284: 'coffee_maker', 285: 'coffee_table', 286: 'coffeepot', 287: 'coil', 288: 'coin', 289: 'colander', 290: 'coleslaw', 291: 'coloring_material', 292: 'combination_lock', 293: 'pacifier', 294: 'comic_book', 295: 'compass', 296: 'computer_keyboard', 297: 'condiment', 298: 'cone', 299: 'control', 300: 'convertible_(automobile)', 301: 'sofa_bed', 302: 'cooker', 303: 'cookie', 304: 'cooking_utensil', 305: 'cooler_(for_food)', 306: 'cork_(bottle_plug)', 307: 'corkboard', 308: 'corkscrew', 309: 'edible_corn', 310: 'cornbread', 311: 'cornet', 312: 'cornice', 313: 'cornmeal', 314: 'corset', 315: 'costume', 316: 'cougar', 317: 'coverall', 318: 'cowbell', 319: 'cowboy_hat', 320: 'crab_(animal)', 321: 'crabmeat', 322: 'cracker', 323: 'crape', 324: 'crate', 325: 'crayon', 326: 'cream_pitcher', 327: 'crescent_roll', 328: 'crib', 329: 'crock_pot', 330: 'crossbar', 331: 'crouton', 332: 'crow', 333: 'crowbar', 334: 'crown', 335: 'crucifix', 336: 'cruise_ship', 337: 'police_cruiser', 338: 'crumb', 339: 'crutch', 340: 'cub_(animal)', 341: 'cube', 342: 'cucumber', 343: 'cufflink', 344: 'cup', 345: 'trophy_cup', 346: 'cupboard', 347: 'cupcake', 348: 'hair_curler', 349: 'curling_iron', 350: 'curtain', 351: 'cushion', 352: 'cylinder', 353: 'cymbal', 354: 'dagger', 355: 'dalmatian', 356: 'dartboard', 357: 'date_(fruit)', 358: 'deck_chair', 359: 'deer', 360: 'dental_floss', 361: 'desk', 362: 'detergent', 363: 'diaper', 364: 'diary', 365: 'die', 366: 'dinghy', 367: 'dining_table', 368: 'tux', 369: 'dish', 370: 'dish_antenna', 371: 'dishrag', 372: 'dishtowel', 373: 'dishwasher', 374: 'dishwasher_detergent', 375: 'dispenser', 376: 'diving_board', 377: 'Dixie_cup', 378: 'dog', 379: 'dog_collar', 380: 'doll', 381: 'dollar', 382: 'dollhouse', 383: 'dolphin', 384: 'domestic_ass', 385: 'doorknob', 386: 'doormat', 387: 'doughnut', 388: 'dove', 389: 'dragonfly', 390: 'drawer', 391: 'underdrawers', 392: 'dress', 393: 'dress_hat', 394: 'dress_suit', 395: 'dresser', 396: 'drill', 397: 'drone', 398: 'dropper', 399: 'drum_(musical_instrument)', 400: 'drumstick', 401: 'duck', 402: 'duckling', 403: 'duct_tape', 404: 'duffel_bag', 405: 'dumbbell', 406: 'dumpster', 407: 'dustpan', 408: 'eagle', 409: 'earphone', 410: 'earplug', 411: 'earring', 412: 'easel', 413: 'eclair', 414: 'eel', 415: 'egg', 416: 'egg_roll', 417: 'egg_yolk', 418: 'eggbeater', 419: 'eggplant', 420: 'electric_chair', 421: 'refrigerator', 422: 'elephant', 423: 'elk', 424: 'envelope', 425: 'eraser', 426: 'escargot', 427: 'eyepatch', 428: 'falcon', 429: 'fan', 430: 'faucet', 431: 'fedora', 432: 'ferret', 433: 'Ferris_wheel', 434: 'ferry', 435: 'fig_(fruit)', 436: 'fighter_jet', 437: 'figurine', 438: 'file_cabinet', 439: 'file_(tool)', 440: 'fire_alarm', 441: 'fire_engine', 442: 'fire_extinguisher', 443: 'fire_hose', 444: 'fireplace', 445: 'fireplug', 446: 'first-aid_kit', 447: 'fish', 448: 'fish_(food)', 449: 'fishbowl', 450: 'fishing_rod', 451: 'flag', 452: 'flagpole', 453: 'flamingo', 454: 'flannel', 455: 'flap', 456: 'flash', 457: 'flashlight', 458: 'fleece', 459: 'flip-flop_(sandal)', 460: 'flipper_(footwear)', 461: 'flower_arrangement', 462: 'flute_glass', 463: 'foal', 464: 'folding_chair', 465: 'food_processor', 466: 'football_(American)', 467: 'football_helmet', 468: 'footstool', 469: 'fork', 470: 'forklift', 471: 'freight_car', 472: 'French_toast', 473: 'freshener', 474: 'frisbee', 475: 'frog', 476: 'fruit_juice', 477: 'frying_pan', 478: 'fudge', 479: 'funnel', 480: 'futon', 481: 'gag', 482: 'garbage', 483: 'garbage_truck', 484: 'garden_hose', 485: 'gargle', 486: 'gargoyle', 487: 'garlic', 488: 'gasmask', 489: 'gazelle', 490: 'gelatin', 491: 'gemstone', 492: 'generator',
                493: 'giant_panda', 494: 'gift_wrap', 495: 'ginger', 496: 'giraffe', 497: 'cincture', 498: 'glass_(drink_container)', 499: 'globe', 500: 'glove', 501: 'goat', 502: 'goggles', 503: 'goldfish', 504: 'golf_club', 505: 'golfcart', 506: 'gondola_(boat)', 507: 'goose', 508: 'gorilla', 509: 'gourd', 510: 'grape', 511: 'grater', 512: 'gravestone', 513: 'gravy_boat', 514: 'green_bean', 515: 'green_onion', 516: 'griddle', 517: 'grill', 518: 'grits', 519: 'grizzly', 520: 'grocery_bag', 521: 'guitar', 522: 'gull', 523: 'gun', 524: 'hairbrush', 525: 'hairnet', 526: 'hairpin', 527: 'halter_top', 528: 'ham', 529: 'hamburger', 530: 'hammer', 531: 'hammock', 532: 'hamper', 533: 'hamster', 534: 'hair_dryer', 535: 'hand_glass', 536: 'hand_towel', 537: 'handcart', 538: 'handcuff', 539: 'handkerchief', 540: 'handle', 541: 'handsaw', 542: 'hardback_book', 543: 'harmonium', 544: 'hat', 545: 'hatbox', 546: 'veil', 547: 'headband', 548: 'headboard', 549: 'headlight', 550: 'headscarf', 551: 'headset', 552: 'headstall_(for_horses)', 553: 'heart', 554: 'heater', 555: 'helicopter', 556: 'helmet', 557: 'heron', 558: 'highchair', 559: 'hinge', 560: 'hippopotamus', 561: 'hockey_stick', 562: 'hog', 563: 'home_plate_(baseball)', 564: 'honey', 565: 'fume_hood', 566: 'hook', 567: 'hookah', 568: 'hornet', 569: 'horse', 570: 'hose', 571: 'hot-air_balloon', 572: 'hotplate', 573: 'hot_sauce', 574: 'hourglass', 575: 'houseboat', 576: 'hummingbird', 577: 'hummus', 578: 'polar_bear', 579: 'icecream', 580: 'popsicle', 581: 'ice_maker', 582: 'ice_pack', 583: 'ice_skate', 584: 'igniter', 585: 'inhaler', 586: 'iPod', 587: 'iron_(for_clothing)', 588: 'ironing_board', 589: 'jacket', 590: 'jam', 591: 'jar', 592: 'jean', 593: 'jeep', 594: 'jelly_bean', 595: 'jersey', 596: 'jet_plane', 597: 'jewel', 598: 'jewelry', 599: 'joystick', 600: 'jumpsuit', 601: 'kayak', 602: 'keg', 603: 'kennel', 604: 'kettle', 605: 'key', 606: 'keycard', 607: 'kilt', 608: 'kimono', 609: 'kitchen_sink', 610: 'kitchen_table', 611: 'kite', 612: 'kitten', 613: 'kiwi_fruit', 614: 'knee_pad', 615: 'knife', 616: 'knitting_needle', 617: 'knob', 618: 'knocker_(on_a_door)', 619: 'koala', 620: 'lab_coat', 621: 'ladder', 622: 'ladle', 623: 'ladybug', 624: 'lamb_(animal)', 625: 'lamb-chop', 626: 'lamp', 627: 'lamppost', 628: 'lampshade', 629: 'lantern', 630: 'lanyard', 631: 'laptop_computer', 632: 'lasagna', 633: 'latch', 634: 'lawn_mower', 635: 'leather', 636: 'legging_(clothing)', 637: 'Lego', 638: 'legume', 639: 'lemon', 640: 'lemonade', 641: 'lettuce', 642: 'license_plate', 643: 'life_buoy', 644: 'life_jacket', 645: 'lightbulb', 646: 'lightning_rod', 647: 'lime', 648: 'limousine', 649: 'lion', 650: 'lip_balm', 651: 'liquor', 652: 'lizard', 653: 'log', 654: 'lollipop', 655: 'speaker_(stereo_equipment)', 656: 'loveseat', 657: 'machine_gun', 658: 'magazine', 659: 'magnet', 660: 'mail_slot', 661: 'mailbox_(at_home)', 662: 'mallard', 663: 'mallet', 664: 'mammoth', 665: 'manatee', 666: 'mandarin_orange', 667: 'manger', 668: 'manhole', 669: 'map', 670: 'marker', 671: 'martini', 672: 'mascot', 673: 'mashed_potato', 674: 'masher', 675: 'mask', 676: 'mast', 677: 'mat_(gym_equipment)', 678: 'matchbox', 679: 'mattress', 680: 'measuring_cup', 681: 'measuring_stick', 682: 'meatball', 683: 'medicine', 684: 'melon', 685: 'microphone', 686: 'microscope', 687: 'microwave_oven', 688: 'milestone', 689: 'milk', 690: 'milk_can', 691: 'milkshake', 692: 'minivan', 693: 'mint_candy', 694: 'mirror', 695: 'mitten', 696: 'mixer_(kitchen_tool)', 697: 'money', 698: 'monitor_(computer_equipment) computer_monitor', 699: 'monkey', 700: 'motor', 701: 'motor_scooter', 702: 'motor_vehicle', 703: 'motorcycle', 704: 'mound_(baseball)', 705: 'mouse_(computer_equipment)', 706: 'mousepad', 707: 'muffin', 708: 'mug', 709: 'mushroom', 710: 'music_stool', 711: 'musical_instrument', 712: 'nailfile', 713: 'napkin', 714: 'neckerchief', 715: 'necklace', 716: 'necktie', 717: 'needle', 718: 'nest', 719: 'newspaper', 720: 'newsstand', 721: 'nightshirt', 722: 'nosebag_(for_animals)', 723: 'noseband_(for_animals)', 724: 'notebook', 725: 'notepad', 726: 'nut', 727: 'nutcracker', 728: 'oar', 729: 'octopus_(food)', 730: 'octopus_(animal)', 731: 'oil_lamp', 732: 'olive_oil', 733: 'omelet', 734: 'onion', 735: 'orange_(fruit)', 736: 'orange_juice', 737: 'ostrich', 738: 'ottoman', 739: 'oven', 740: 'overalls_(clothing)', 741: 'owl', 742: 'packet', 743: 'inkpad', 744: 'pad', 745: 'paddle', 746: 'padlock', 747: 'paintbrush', 748: 'painting', 749: 'pajamas', 750: 'palette', 751: 'pan_(for_cooking)', 752: 'pan_(metal_container)',
                753: 'pancake', 754: 'pantyhose', 755: 'papaya', 756: 'paper_plate', 757: 'paper_towel', 758: 'paperback_book', 759: 'paperweight', 760: 'parachute', 761: 'parakeet', 762: 'parasail_(sports)', 763: 'parasol', 764: 'parchment', 765: 'parka', 766: 'parking_meter', 767: 'parrot', 768: 'passenger_car_(part_of_a_train)', 769: 'passenger_ship', 770: 'passport', 771: 'pastry', 772: 'patty_(food)', 773: 'pea_(food)', 774: 'peach', 775: 'peanut_butter', 776: 'pear', 777: 'peeler_(tool_for_fruit_and_vegetables)', 778: 'wooden_leg', 779: 'pegboard', 780: 'pelican', 781: 'pen', 782: 'pencil', 783: 'pencil_box', 784: 'pencil_sharpener', 785: 'pendulum', 786: 'penguin', 787: 'pennant', 788: 'penny_(coin)', 789: 'pepper', 790: 'pepper_mill', 791: 'perfume', 792: 'persimmon', 793: 'person', 794: 'pet', 795: 'pew_(church_bench)', 796: 'phonebook', 797: 'phonograph_record', 798: 'piano', 799: 'pickle', 800: 'pickup_truck', 801: 'pie', 802: 'pigeon', 803: 'piggy_bank', 804: 'pillow', 805: 'pin_(non_jewelry)', 806: 'pineapple', 807: 'pinecone', 808: 'ping-pong_ball', 809: 'pinwheel', 810: 'tobacco_pipe', 811: 'pipe', 812: 'pistol', 813: 'pita_(bread)', 814: 'pitcher_(vessel_for_liquid)', 815: 'pitchfork', 816: 'pizza', 817: 'place_mat', 818: 'plate', 819: 'platter', 820: 'playpen', 821: 'pliers', 822: 'plow_(farm_equipment)', 823: 'plume', 824: 'pocket_watch', 825: 'pocketknife', 826: 'poker_(fire_stirring_tool)', 827: 'pole', 828: 'polo_shirt', 829: 'poncho', 830: 'pony', 831: 'pool_table', 832: 'pop_(soda)', 833: 'postbox_(public)', 834: 'postcard', 835: 'poster', 836: 'pot', 837: 'flowerpot', 838: 'potato', 839: 'potholder', 840: 'pottery', 841: 'pouch', 842: 'power_shovel', 843: 'prawn', 844: 'pretzel', 845: 'printer', 846: 'projectile_(weapon)', 847: 'projector', 848: 'propeller', 849: 'prune', 850: 'pudding', 851: 'puffer_(fish)', 852: 'puffin', 853: 'pug-dog', 854: 'pumpkin', 855: 'puncher', 856: 'puppet', 857: 'puppy', 858: 'quesadilla', 859: 'quiche', 860: 'quilt', 861: 'rabbit', 862: 'race_car', 863: 'racket', 864: 'radar', 865: 'radiator', 866: 'radio_receiver', 867: 'radish', 868: 'raft', 869: 'rag_doll', 870: 'raincoat', 871: 'ram_(animal)', 872: 'raspberry', 873: 'rat', 874: 'razorblade', 875: 'reamer_(juicer)', 876: 'rearview_mirror', 877: 'receipt', 878: 'recliner', 879: 'record_player', 880: 'reflector', 881: 'remote_control', 882: 'rhinoceros', 883: 'rib_(food)', 884: 'rifle', 885: 'ring', 886: 'river_boat', 887: 'road_map', 888: 'robe', 889: 'rocking_chair', 890: 'rodent', 891: 'roller_skate', 892: 'Rollerblade', 893: 'rolling_pin', 894: 'root_beer', 895: 'router_(computer_equipment)', 896: 'rubber_band', 897: 'runner_(carpet)', 898: 'plastic_bag', 899: 'saddle_(on_an_animal)', 900: 'saddle_blanket', 901: 'saddlebag', 902: 'safety_pin', 903: 'sail', 904: 'salad', 905: 'salad_plate', 906: 'salami', 907: 'salmon_(fish)', 908: 'salmon_(food)', 909: 'salsa', 910: 'saltshaker', 911: 'sandal_(type_of_shoe)', 912: 'sandwich', 913: 'satchel', 914: 'saucepan', 915: 'saucer', 916: 'sausage', 917: 'sawhorse', 918: 'saxophone', 919: 'scale_(measuring_instrument)', 920: 'scarecrow', 921: 'scarf', 922: 'school_bus', 923: 'scissors', 924: 'scoreboard', 925: 'scraper', 926: 'screwdriver', 927: 'scrubbing_brush', 928: 'sculpture', 929: 'seabird', 930: 'seahorse', 931: 'seaplane', 932: 'seashell', 933: 'sewing_machine', 934: 'shaker', 935: 'shampoo', 936: 'shark', 937: 'sharpener', 938: 'Sharpie', 939: 'shaver_(electric)', 940: 'shaving_cream', 941: 'shawl', 942: 'shears', 943: 'sheep', 944: 'shepherd_dog', 945: 'sherbert', 946: 'shield', 947: 'shirt', 948: 'shoe', 949: 'shopping_bag', 950: 'shopping_cart', 951: 'short_pants', 952: 'shot_glass', 953: 'shoulder_bag', 954: 'shovel', 955: 'shower_head', 956: 'shower_cap', 957: 'shower_curtain', 958: 'shredder_(for_paper)', 959: 'signboard', 960: 'silo', 961: 'sink', 962: 'skateboard', 963: 'skewer', 964: 'ski', 965: 'ski_boot', 966: 'ski_parka', 967: 'ski_pole', 968: 'skirt', 969: 'skullcap', 970: 'sled', 971: 'sleeping_bag', 972: 'sling_(bandage)', 973: 'slipper_(footwear)', 974: 'smoothie', 975: 'snake', 976: 'snowboard', 977: 'snowman', 978: 'snowmobile', 979: 'soap', 980: 'soccer_ball', 981: 'sock', 982: 'sofa', 983: 'softball', 984: 'solar_array', 985: 'sombrero', 986: 'soup', 987: 'soup_bowl', 988: 'soupspoon', 989: 'sour_cream', 990: 'soya_milk', 991: 'space_shuttle', 992: 'sparkler_(fireworks)', 993: 'spatula', 994: 'spear', 995: 'spectacles', 996: 'spice_rack', 997: 'spider', 998: 'crawfish', 999: 'sponge', 1000: 'spoon', 1001: 'sportswear', 
                1002: 'spotlight', 1003: 'squid_(food)', 1004: 'squirrel', 1005: 'stagecoach', 1006: 'stapler_(stapling_machine)', 1007: 'starfish', 1008: 'statue_(sculpture)', 1009: 'steak_(food)', 1010: 'steak_knife', 1011: 'steering_wheel', 1012: 'stepladder', 1013: 'step_stool', 1014: 'stereo_(sound_system)', 1015: 'stew', 1016: 'stirrer', 1017: 'stirrup', 1018: 'stool', 1019: 'stop_sign', 1020: 'brake_light', 1021: 'stove', 1022: 'strainer', 1023: 'strap', 1024: 'straw_(for_drinking)', 1025: 'strawberry', 1026: 'street_sign', 1027: 'streetlight', 1028: 'string_cheese', 1029: 'stylus', 1030: 'subwoofer', 1031: 'sugar_bowl', 1032: 'sugarcane_(plant)', 1033: 'suit_(clothing)', 1034: 'sunflower', 1035: 'sunglasses', 1036: 'sunhat', 1037: 'surfboard', 1038: 'sushi', 1039: 'mop', 1040: 'sweat_pants', 1041: 'sweatband', 1042: 'sweater', 1043: 'sweatshirt', 1044: 'sweet_potato', 1045: 'swimsuit', 1046: 'sword', 1047: 'syringe', 1048: 'Tabasco_sauce', 1049: 'table-tennis_table', 1050: 'table', 1051: 'table_lamp', 1052: 'tablecloth', 1053: 'tachometer', 1054: 'taco', 1055: 'tag', 1056: 'taillight', 1057: 'tambourine', 1058: 'army_tank', 1059: 'tank_(storage_vessel)', 1060: 'tank_top_(clothing)', 1061: 'tape_(sticky_cloth_or_paper)', 1062: 'tape_measure', 1063: 'tapestry', 1064: 'tarp', 1065: 'tartan', 1066: 'tassel', 1067: 'tea_bag', 1068: 'teacup', 1069: 'teakettle', 1070: 'teapot', 1071: 'teddy_bear', 1072: 'telephone', 1073: 'telephone_booth', 1074: 'telephone_pole', 1075: 'telephoto_lens', 1076: 'television_camera', 1077: 'television_set', 1078: 'tennis_ball', 1079: 'tennis_racket', 1080: 'tequila', 1081: 'thermometer', 1082: 'thermos_bottle', 1083: 'thermostat', 1084: 'thimble', 1085: 'thread', 1086: 'thumbtack', 1087: 'tiara', 1088: 'tiger', 1089: 'tights_(clothing)', 1090: 'timer', 1091: 'tinfoil', 1092: 'tinsel', 1093: 'tissue_paper', 1094: 'toast_(food)', 1095: 'toaster', 1096: 'toaster_oven', 1097: 'toilet', 1098: 'toilet_tissue', 1099: 'tomato', 1100: 'tongs', 1101: 'toolbox', 1102: 'toothbrush', 1103: 'toothpaste', 1104: 'toothpick', 1105: 'cover', 1106: 'tortilla', 1107: 'tow_truck', 1108: 'towel', 1109: 'towel_rack', 1110: 'toy', 1111: 'tractor_(farm_equipment)', 1112: 'traffic_light', 1113: 'dirt_bike', 1114: 'trailer_truck', 1115: 'train_(railroad_vehicle)', 1116: 'trampoline', 1117: 'tray', 1118: 'trench_coat', 1119: 'triangle_(musical_instrument)', 1120: 'tricycle', 1121: 'tripod', 1122: 'trousers', 1123: 'truck', 1124: 'truffle_(chocolate)', 1125: 'trunk', 1126: 'vat', 1127: 'turban', 1128: 'turkey_(food)', 1129: 'turnip', 1130: 'turtle', 1131: 'turtleneck_(clothing)', 1132: 'typewriter', 1133: 'umbrella', 1134: 'underwear', 1135: 'unicycle', 1136: 'urinal', 1137: 'urn', 1138: 'vacuum_cleaner', 1139: 'vase', 1140: 'vending_machine', 1141: 'vent', 1142: 'vest', 1143: 'videotape', 1144: 'vinegar', 1145: 'violin', 1146: 'vodka', 1147: 'volleyball', 1148: 'vulture', 1149: 'waffle', 1150: 'waffle_iron', 1151: 'wagon', 1152: 'wagon_wheel', 1153: 'walking_stick', 1154: 'wall_clock', 1155: 'wall_socket', 1156: 'wallet', 1157: 'walrus', 1158: 'wardrobe', 1159: 'washbasin', 1160: 'automatic_washer', 1161: 'watch', 1162: 'water_bottle', 1163: 'water_cooler', 1164: 'water_faucet', 1165: 'water_heater', 1166: 'water_jug', 1167: 'water_gun', 1168: 'water_scooter', 1169: 'water_ski', 1170: 'water_tower', 1171: 'watering_can', 1172: 'watermelon', 1173: 'weathervane', 1174: 'webcam', 1175: 'wedding_cake', 1176: 'wedding_ring', 1177: 'wet_suit', 1178: 'wheel', 1179: 'wheelchair', 1180: 'whipped_cream', 1181: 'whistle', 1182: 'wig', 1183: 'wind_chime', 1184: 'windmill', 1185: 'window_box_(for_plants)', 1186: 'windshield_wiper', 1187: 'windsock', 1188: 'wine_bottle', 1189: 'wine_bucket', 1190: 'wineglass', 1191: 'blinder_(for_horses)', 1192: 'wok', 1193: 'wolf', 1194: 'wooden_spoon', 1195: 'wreath', 1196: 'wrench', 1197: 'wristband', 1198: 'wristlet', 1199: 'yacht', 1200: 'yogurt', 1201: 'yoke_(animal_equipment)', 1202: 'zebra', 1203: 'zucchini'}

Frequency_all = {1: 64, 2: 364, 3: 1911, 4: 149, 5: 29, 6: 26, 7: 59, 8: 22, 9: 12, 10: 28, 11: 505, 12: 1207, 13: 4, 14: 10, 15: 500, 16: 33, 17: 3, 18: 44, 19: 561, 20: 8, 21: 9, 22: 33, 23: 1883, 24: 98, 25: 70, 26: 46, 27: 117, 28: 41, 29: 1395, 30: 7, 31: 1, 32: 314, 33: 31, 34: 1905, 35: 1859, 36: 1623, 37: 47, 38: 3, 39: 3, 40: 1, 41: 305, 42: 6, 43: 210, 44: 36, 45: 1787, 46: 17, 47: 51, 48: 138, 49: 3, 50: 1470, 51: 3, 52: 2, 53: 186, 54: 76, 55: 26, 56: 303, 57: 738, 58: 1799, 59: 1934, 60: 1609, 61: 1622, 62: 41, 63: 4, 64: 11, 65: 270, 66: 349, 67: 42, 68: 823, 69: 6, 70: 48, 71: 3, 72: 42, 73: 24, 74: 16, 75: 605, 76: 646, 77: 1765, 78: 2, 79: 125, 80: 1420, 81: 140, 82: 4, 83: 322, 84: 60, 85: 2, 86: 231, 87: 333, 88: 1941, 89: 367, 90: 1922, 91: 18, 92: 81, 93: 1, 94: 1852, 95: 430, 96: 247, 97: 94, 98: 21, 99: 1821, 100: 16, 101: 12, 102: 25, 103: 41, 104: 244, 105: 7, 106: 1, 107: 40, 108: 40, 109: 104, 110: 1671, 111: 49, 112: 243, 113: 2, 114: 242, 115: 271, 116: 104, 117: 8, 118: 1758, 119: 1, 120: 48, 121: 14, 122: 40, 123: 1, 124: 37, 125: 1510, 126: 6, 127: 1903, 128: 70, 129: 86, 130: 7, 131: 5, 132: 1406, 133: 1901, 134: 15, 135: 28, 136: 6, 137: 494, 138: 234, 139: 1922, 140: 1, 141: 35, 142: 5, 143: 1828, 144: 8, 145: 63, 146: 1668, 147: 4, 148: 95, 149: 17, 150: 1567, 151: 2, 152: 103, 153: 50, 154: 1309, 155: 6, 156: 92, 157: 19, 158: 37, 159: 4, 160: 709, 161: 9, 162: 82, 163: 15, 164: 3, 165: 61, 166: 51, 167: 5, 168: 13, 169: 642, 170: 24, 171: 255, 172: 9, 173: 1808, 174: 31, 175: 158, 176: 80, 177: 1884, 178: 158, 179: 2, 180: 12, 181: 1659, 182: 7, 183: 834, 184: 57, 185: 174, 186: 95, 187: 27, 188: 22, 189: 1391, 190: 90, 191: 40, 192: 445, 193: 21, 194: 1132, 195: 177, 196: 4, 197: 17, 198: 84, 199: 55, 200: 30, 201: 25, 202: 2, 203: 125, 204: 1135, 205: 19, 206: 72, 207: 1926, 208: 159, 209: 7, 210: 1, 211: 13, 212: 35, 213: 18, 214: 8, 215: 6, 216: 35, 217: 1222, 218: 103, 219: 28, 220: 63, 221: 28, 222: 5, 223: 7, 224: 14, 225: 1918, 226: 133, 227: 16, 228: 27, 229: 110, 230: 1895, 231: 4, 232: 1927, 233: 8, 234: 1, 235: 263, 236: 10, 237: 2, 238: 3, 239: 87, 240: 9, 241: 71, 242: 13, 243: 18, 244: 2, 245: 5, 246: 45, 247: 1, 248: 23, 249: 32, 250: 4, 251: 1, 252: 858, 253: 661, 254: 168, 255: 210, 256: 65, 257: 4, 258: 2, 259: 159, 260: 31, 261: 811, 262: 1, 263: 42, 264: 27, 265: 2, 266: 5, 267: 95, 268: 32, 269: 1, 270: 1, 271: 1844, 272: 897, 273: 31, 274: 23, 275: 1, 276: 202, 277: 746, 278: 44, 279: 14, 280: 26, 281: 1, 282: 2, 283: 25, 284: 238, 285: 592, 286: 26, 287: 5, 288: 42, 289: 13, 290: 46, 291: 1, 292: 8, 293: 34, 294: 5, 295: 1, 296: 1871, 297: 717, 298: 1010, 299: 679, 300: 3, 301: 4, 302: 1, 303: 166, 304: 2, 305: 266, 306: 101, 307: 6, 308: 14, 309: 133, 310: 2, 311: 38, 312: 95, 313: 1, 314: 12, 315: 49, 316: 5, 317: 5, 318: 16, 319: 216, 320: 12, 321: 1, 322: 54, 323: 5, 324: 245, 325: 12, 326: 7, 327: 35, 328: 36, 329: 32, 330: 1027, 331: 10, 332: 12, 333: 1, 334: 67, 335: 71, 336: 30, 337: 48, 338: 249, 339: 13, 340: 29, 341: 14, 342: 236, 343: 15, 344: 1521, 345: 25, 346: 249, 347: 139, 348: 2, 349: 2, 350: 1890, 351: 1240, 352: 1, 353: 9, 354: 1, 355: 3, 356: 11, 357: 4, 358: 236, 359: 44, 360: 19, 361: 1100, 362: 7, 363: 69, 364: 2, 365: 8, 366: 5, 367: 227, 368: 6, 369: 106, 370: 81, 371: 17, 372: 134, 373: 312, 374: 8, 375: 271, 376: 2, 377: 103, 378: 1938, 379: 574, 380: 120, 381: 2, 382: 2, 383: 13, 384: 29, 385: 1710, 386: 66, 387: 1008, 388: 1, 389: 3, 390: 1942, 391: 19, 392: 1488, 393: 46, 394: 106, 395: 115, 396: 19, 397: 2, 398: 1, 399: 28, 400: 9, 401: 192, 402: 12, 403: 21, 404: 247, 405: 6, 406: 64, 407: 7, 408: 40, 409: 542, 410: 2, 411: 1898, 412: 36, 413: 4, 414: 1, 415: 191, 416: 6, 417: 41, 418: 39, 419: 46, 420: 1, 421: 1451, 422: 1878, 423: 11, 424: 82, 425: 18, 426: 1, 427: 7, 428: 3, 429: 575, 430: 1907, 431: 8, 432: 4, 433: 32, 434: 11, 435: 4, 436: 54, 437: 202, 438: 32, 439: 3, 440: 130, 441: 119, 442: 141, 443: 29, 444: 525, 445: 1323, 446: 2, 447: 113, 448: 16, 449: 7, 450: 35, 451: 1908, 452: 353, 453: 18, 454: 14, 455: 77, 456: 8, 457: 37, 458: 1, 459: 346, 460: 19, 461: 1779, 462: 23, 463: 25, 464: 67, 465: 19, 466: 28, 467: 4, 468: 27, 469: 1861, 470: 11, 471: 13, 472: 13, 473: 32, 474: 1767, 475: 42, 476: 17, 477: 128, 478: 1, 479: 9, 480: 10, 481: 4, 482: 9, 483: 18, 484: 41, 485: 28, 486: 3, 487: 65, 488: 9, 489: 23, 490: 24, 491: 1, 492: 2, 493: 59, 494: 48, 495: 17, 496: 1877, 497: 18, 498: 1920, 499: 50, 500: 1890, 501: 99, 502: 1530, 503: 3, 504: 11, 505: 19, 506: 3, 507: 63, 508: 5, 509: 6, 510: 233, 511: 54, 
                512: 36, 513: 10, 514: 124, 515: 101, 516: 3, 517: 363, 518: 3, 519: 30, 520: 18, 521: 199, 522: 97, 523: 32, 524: 121, 525: 16, 526: 12, 527: 2, 528: 214, 529: 48, 530: 26, 531: 13, 532: 4, 533: 11, 534: 123, 535: 7, 536: 200, 537: 91, 538: 9, 539: 72, 540: 1886, 541: 4, 542: 1, 543: 1, 544: 1932, 545: 4, 546: 56, 547: 854, 548: 755, 549: 1843, 550: 96, 551: 7, 552: 74, 553: 66, 554: 57, 555: 44, 556: 1905, 557: 4, 558: 90, 559: 1635, 560: 8, 561: 5, 562: 50, 563: 545, 564: 20, 565: 193, 566: 285, 567: 3, 568: 1, 569: 1904, 570: 294, 571: 3, 572: 5, 573: 24, 574: 2, 575: 2, 576: 16, 577: 8, 578: 154, 579: 66, 580: 1, 581: 24, 582: 1, 583: 4, 584: 75, 585: 6, 586: 126, 587: 24, 588: 22, 589: 1872, 590: 16, 591: 423, 592: 1927, 593: 38, 594: 3, 595: 1945, 596: 35, 597: 1, 598: 13, 599: 9, 600: 14, 601: 37, 602: 3, 603: 4, 604: 100, 605: 195, 606: 1, 607: 12, 608: 24, 609: 489, 610: 10, 611: 1689, 612: 42, 613: 81, 614: 894, 615: 1868, 616: 7, 617: 1567, 618: 10, 619: 8, 620: 7, 621: 629, 622: 89, 623: 15, 624: 134, 625: 4, 626: 1802, 627: 595, 628: 1210, 629: 48, 630: 418, 631: 1846, 632: 5, 633: 221, 634: 10, 635: 7, 636: 76, 637: 22, 638: 10, 639: 341, 640: 1, 641: 705, 642: 1900, 643: 188, 644: 227, 645: 861, 646: 6, 647: 115, 648: 5, 649: 43, 650: 14, 651: 6, 652: 15, 653: 1167, 654: 15, 655: 994, 656: 28, 657: 2, 658: 338, 659: 334, 660: 15, 661: 102, 662: 1, 663: 8, 664: 1, 665: 1, 666: 28, 667: 91, 668: 260, 669: 131, 670: 128, 671: 3, 672: 10, 673: 39, 674: 2, 675: 925, 676: 354, 677: 31, 678: 10, 679: 215, 680: 71, 681: 43, 682: 28, 683: 34, 684: 16, 685: 273, 686: 2, 687: 999, 688: 4, 689: 107, 690: 2, 691: 1, 692: 454, 693: 9, 694: 1901, 695: 61, 696: 91, 697: 46, 698: 1402, 699: 74, 700: 421, 701: 226, 702: 10, 703: 1720, 704: 261, 705: 1337, 706: 293, 707: 62, 708: 814, 709: 407, 710: 6, 711: 16, 712: 7, 713: 1791, 714: 2, 715: 1915, 716: 1940, 717: 13, 718: 16, 719: 448, 720: 12, 721: 18, 722: 4, 723: 71, 724: 189, 725: 74, 726: 103, 727: 3, 728: 110, 729: 5, 730: 9, 731: 15, 732: 25, 733: 7, 734: 647, 735: 824, 736: 100, 737: 47, 738: 121, 739: 731, 740: 73, 741: 49, 742: 23, 743: 4, 744: 62, 745: 118, 746: 99, 747: 40, 748: 1036, 749: 105, 750: 21, 751: 229, 752: 7, 753: 72, 754: 9, 755: 10, 756: 328, 757: 468, 758: 1, 759: 2, 760: 24, 761: 11, 762: 72, 763: 17, 764: 10, 765: 17, 766: 489, 767: 47, 768: 93, 769: 1, 770: 12, 771: 228, 772: 5, 773: 76, 774: 71, 775: 30, 776: 109, 777: 14, 778: 1, 779: 8, 780: 26, 781: 339, 782: 153, 783: 2, 784: 3, 785: 8, 786: 47, 787: 8, 788: 6, 789: 116, 790: 69, 791: 13, 792: 6, 793: 1928, 794: 79, 795: 14, 796: 7, 797: 20, 798: 114, 799: 221, 800: 502, 801: 62, 802: 87, 803: 4, 804: 1912, 805: 7, 806: 186, 807: 18, 808: 4, 809: 3, 810: 7, 811: 1413, 812: 7, 813: 12, 814: 248, 815: 4, 816: 1881, 817: 529, 818: 1932, 819: 50, 820: 3, 821: 28, 822: 10, 823: 5, 824: 5, 825: 18, 826: 14, 827: 1890, 828: 660, 829: 8, 830: 25, 831: 10, 832: 218, 833: 36, 834: 16, 835: 808, 836: 479, 837: 1404, 838: 307, 839: 57, 840: 28, 841: 80, 842: 11, 843: 92, 844: 20, 845: 194, 846: 23, 847: 52, 848: 673, 849: 2, 850: 2, 851: 1, 852: 2, 853: 8, 854: 80, 855: 3, 856: 3, 857: 15, 858: 2, 859: 10, 860: 386, 861: 65, 862: 3, 863: 35, 864: 5, 865: 180, 866: 99, 867: 49, 868: 28, 869: 1, 870: 52, 871: 36, 872: 70, 873: 6, 874: 29, 875: 24, 876: 1115, 877: 61, 878: 18, 879: 18, 880: 665, 881: 1096, 882: 29, 883: 8, 884: 14, 885: 1622, 886: 2, 887: 3, 888: 32, 889: 55, 890: 1, 891: 10, 892: 10, 893: 47, 894: 3, 895: 29, 896: 342, 897: 25, 898: 1469, 899: 521, 900: 347, 901: 35, 902: 7, 903: 207, 904: 108, 905: 2, 906: 34, 907: 12, 908: 10, 909: 13, 910: 361, 911: 1023, 912: 782, 913: 2, 914: 5, 915: 247, 916: 221, 917: 4, 918: 8, 919: 158, 920: 3, 921: 752, 922: 64, 923: 707, 924: 143, 925: 1, 926: 49, 927: 126, 928: 76, 929: 11, 930: 11, 931: 4, 932: 39, 933: 11, 934: 13, 935: 91, 936: 14, 937: 5, 938: 3, 939: 10, 940: 18, 941: 9, 942: 6, 943: 951, 944: 2, 945: 1, 946: 19, 947: 1942, 948: 1916, 949: 139, 950: 43, 951: 1969, 952: 5, 953: 134, 954: 74, 955: 381, 956: 1, 957: 381, 958: 6, 959: 1826, 960: 28, 961: 1635, 962: 1967, 963: 16, 964: 1926, 965: 1789, 966: 401, 967: 1968, 968: 1167, 969: 1, 970: 56, 971: 17, 972: 1, 973: 58, 974: 9, 975: 8, 976: 1124, 977: 31, 978: 16, 979: 491, 980: 432, 981: 1945, 982: 1899, 983: 5, 984: 28, 985: 7, 986: 146, 987: 1, 988: 25, 989: 22, 990: 1, 991: 10, 992: 9, 993: 308, 994: 4, 995: 1969, 996: 45, 997: 12, 998: 1, 999: 85, 1000: 1127, 1001: 11, 1002: 60, 1003: 1, 1004: 16, 1005: 1, 1006: 65, 1007: 13, 1008: 655, 1009: 51, 1010: 1, 
                1011: 673, 1012: 5, 1013: 36, 1014: 54, 1015: 5, 1016: 8, 1017: 305, 1018: 297, 1019: 1053, 1020: 223, 1021: 1037, 1022: 63, 1023: 1881, 1024: 507, 1025: 333, 1026: 1911, 1027: 1765, 1028: 1, 1029: 5, 1030: 1, 1031: 9, 1032: 2, 1033: 151, 1034: 82, 1035: 1931, 1036: 41, 1037: 1895, 1038: 24, 1039: 22, 1040: 35, 1041: 69, 1042: 962, 1043: 588, 1044: 21, 1045: 825, 1046: 52, 1047: 5, 1048: 5, 1049: 5, 1050: 1860, 1051: 56, 1052: 1582, 1053: 7, 1054: 2, 1055: 1562, 1056: 1885, 1057: 1, 1058: 5, 1059: 137, 1060: 1094, 1061: 134, 1062: 29, 1063: 22, 1064: 522, 1065: 50, 1066: 68, 1067: 16, 1068: 40, 1069: 35, 1070: 135, 1071: 1413, 1072: 772, 1073: 50, 1074: 1015, 1075: 1, 1076: 65, 1077: 1900, 1078: 1302, 1079: 1977, 1080: 2, 1081: 29, 1082: 36, 1083: 138, 1084: 4, 1085: 67, 1086: 26, 1087: 25, 1088: 33, 1089: 37, 
                1090: 50, 1091: 270, 1092: 12, 1093: 316, 1094: 41, 1095: 224, 1096: 105, 1097: 1925, 1098: 1021, 1099: 1213, 1100: 172, 1101: 28, 1102: 745, 1103: 187, 1104: 147, 1105: 136, 1106: 34, 1107: 41, 1108: 636, 1109: 570, 1110: 1149, 1111: 61, 1112: 1890, 1113: 18, 1114: 143, 1115: 1517, 1116: 7, 1117: 943, 1118: 6, 1119: 1, 1120: 11, 1121: 101, 1122: 1909, 1123: 800, 1124: 1, 1125: 44, 1126: 3, 1127: 44, 1128: 31, 1129: 7, 1130: 20, 1131: 11, 1132: 13, 1133: 1924, 1134: 113, 1135: 2, 1136: 139, 1137: 12, 1138: 37, 1139: 1866, 1140: 47, 1141: 1468, 1142: 729, 1143: 24, 1144: 1, 1145: 10, 1146: 3, 1147: 14, 1148: 4, 1149: 29, 1150: 4, 1151: 70, 1152: 46, 1153: 14, 1154: 48, 1155: 1855, 1156: 113, 1157: 1, 1158: 1, 1159: 10, 1160: 54, 1161: 1923, 1162: 630, 1163: 31, 1164: 69, 1165: 7, 1166: 11, 1167: 1, 1168: 30, 1169: 50, 1170: 45, 1171: 28, 1172: 114, 1173: 193, 1174: 21, 1175: 91, 1176: 31, 1177: 1469, 1178: 1924, 1179: 87, 1180: 77, 1181: 11, 1182: 47, 1183: 21, 1184: 47, 1185: 70, 1186: 1838, 1187: 19, 1188: 531, 1189: 11, 1190: 941, 1191: 113, 1192: 26, 1193: 5, 1194: 56, 1195: 73, 1196: 32, 1197: 128, 1198: 623, 1199: 12, 1200: 52, 1201: 11, 1202: 1674, 1203: 81}

Frequency_all_2 = {1: 0.0006439409184207349, 2: 0.0036624139735179297, 3: 0.01922767336096913, 4: 0.0014991749506982734, 5: 0.0002917857286593955, 6: 0.00026160099810842356, 7: 0.000593633034169115, 8: 0.0002213546907071276, 9: 0.0001207389222038878, 10: 0.0002817241518090715, 11: 0.005081096309413611, 12: 0.012144323258341047, 13: 4.0246307401295933e-05, 14: 0.00010061576850323983, 15: 0.005030788425161991, 16: 0.00033203203606069144, 17: 3.018473055097195e-05, 18: 0.0004427093814142552, 19: 0.005644544613031754, 20: 8.049261480259187e-05, 21: 9.055419165291584e-05, 22: 0.00033203203606069144, 23: 0.01894594920916006, 24: 0.0009860345313317503, 25: 0.0007043103795226788, 26: 0.0004628325351149032, 27: 0.001177204491487906, 28: 0.0004125246508632833, 29: 0.014035899706201956, 30: 7.043103795226788e-05, 31: 1.0061576850323983e-05, 32: 0.0031593351310017304, 33: 0.0003119088823600435, 34: 0.019167303899867188, 35: 0.018704471364752284, 36: 0.016329939228075824, 37: 0.0004728941119652272, 38: 3.018473055097195e-05, 39: 3.018473055097195e-05, 40: 1.0061576850323983e-05, 41: 0.0030687809393488146, 42: 6.03694611019439e-05, 43: 0.0021129311385680366, 44: 0.00036221676661166337, 45: 0.017980037831528958, 46: 0.0001710468064555077, 47: 0.0005131404193665231, 48: 0.0013884976053447097, 49: 3.018473055097195e-05, 50: 0.014790517969976255, 51: 3.018473055097195e-05, 52: 2.0123153700647967e-05, 53: 0.0018714532941602607, 54: 0.0007646798406246227, 55: 0.00026160099810842356, 56: 0.003048657785648167, 57: 0.0074254437155391, 58: 0.018100776753732846, 59: 0.019459089628526584, 60: 0.01618907715217129, 61: 0.0163198776512255, 62: 0.0004125246508632833, 63: 4.0246307401295933e-05, 64: 0.0001106773453535638, 65: 0.0027166257495874753, 66: 0.00351149032076307, 67: 0.00042258622771360727, 68: 0.008280677747816639, 69: 6.03694611019439e-05, 70: 0.0004829556888155512, 71: 3.018473055097195e-05, 72: 0.00042258622771360727, 73: 0.0002414778444077756, 74: 0.00016098522960518373, 75: 0.00608725399444601, 76: 0.0064997786453092924, 77: 0.01775868314082183, 78: 2.0123153700647967e-05, 79: 0.0012576971062904978, 80: 0.014287439127460055, 81: 0.0014086207590453576, 82: 4.0246307401295933e-05, 83: 0.0032398277458043226, 84: 0.0006036946110194389, 85: 2.0123153700647967e-05, 86: 0.00232422425242484, 87: 0.003350505091157886, 88: 0.01952952066647885, 89: 0.0036925987040689018, 90: 0.019338350706322695, 91: 0.00018110838330583168, 92: 0.0008149877248762426, 93: 1.0061576850323983e-05, 94: 0.018634040326800015, 95: 0.0043264780456393126, 96: 0.002485209482030024, 97: 0.0009457882239304544, 98: 0.00021129311385680364, 99: 0.018322131444439973, 100: 0.00016098522960518373, 101: 0.0001207389222038878, 102: 0.0002515394212580996, 103: 0.0004125246508632833, 104: 0.002455024751479052, 105: 7.043103795226788e-05, 106: 1.0061576850323983e-05, 107: 0.0004024630740129593, 108: 0.0004024630740129593, 109: 0.0010464039924336943, 110: 0.016812894916891374, 111: 0.0004930172656658751, 112: 0.002444963174628728, 113: 2.0123153700647967e-05, 114: 0.0024349015977784038, 115: 0.0027266873264377993, 116: 0.0010464039924336943, 117: 8.049261480259187e-05, 118: 0.01768825210286956, 119: 1.0061576850323983e-05, 120: 0.0004829556888155512, 121: 0.00014086207590453576, 122: 0.0004024630740129593, 123: 1.0061576850323983e-05, 124: 0.00037227834346198734, 125: 0.015192981043989215, 126: 6.03694611019439e-05, 127: 0.019147180746166538, 128: 0.0007043103795226788, 129: 0.0008652956091278625, 130: 7.043103795226788e-05, 131: 5.0307884251619915e-05, 132: 0.014146577051555519, 133: 0.01912705759246589, 134: 0.00015092365275485973, 135: 0.0002817241518090715, 136: 6.03694611019439e-05, 137: 0.004970418964060048, 138: 0.002354408982975812, 139: 0.019338350706322695, 140: 1.0061576850323983e-05, 141: 0.0003521551897613394, 142: 5.0307884251619915e-05, 143: 0.018392562482392242, 144: 8.049261480259187e-05, 145: 0.0006338793415704109, 146: 0.016782710186340404, 147: 4.0246307401295933e-05, 148: 0.0009558498007807783, 149: 0.0001710468064555077, 150: 0.01576649092445768, 151: 2.0123153700647967e-05, 152: 0.0010363424155833702, 153: 0.0005030788425161992, 154: 0.013170604097074094, 155: 6.03694611019439e-05, 156: 0.0009256650702298064, 157: 0.00019116996015615569, 158: 0.00037227834346198734, 159: 4.0246307401295933e-05, 160: 0.007133657986879704, 161: 9.055419165291584e-05, 162: 0.0008250493017265666, 163: 0.00015092365275485973, 164: 3.018473055097195e-05, 165: 0.000613756187869763, 166: 0.0005131404193665231, 167: 5.0307884251619915e-05, 168: 0.00013080049905421178, 169: 0.006459532337907997, 170: 0.0002414778444077756, 171: 0.0025657020968326157, 172: 9.055419165291584e-05, 173: 0.01819133094538576, 174: 0.0003119088823600435, 175: 0.0015897291423511892, 176: 0.0008049261480259186, 177: 0.018956010786010384, 178: 0.0015897291423511892, 179: 2.0123153700647967e-05, 180: 0.0001207389222038878, 181: 0.01669215599468749, 182: 7.043103795226788e-05, 183: 0.008391355093170202, 184: 0.0005735098804684671, 185: 0.001750714371956373, 186: 0.0009558498007807783, 187: 0.00027166257495874754, 188: 0.0002213546907071276, 189: 0.01399565339880066, 190: 0.0009055419165291585, 191: 0.0004024630740129593, 192: 0.004477401698394172, 193: 0.00021129311385680364, 194: 0.011389704994566749, 195: 0.001780899102507345, 196: 4.0246307401295933e-05, 197: 0.0001710468064555077, 198: 0.0008451724554272145, 199: 0.0005533867267678191, 200: 0.00030184730550971946, 201: 0.0002515394212580996, 202: 2.0123153700647967e-05, 203: 0.0012576971062904978, 204: 0.01141988972511772, 205: 0.00019116996015615569, 206: 0.0007244335332233267, 207: 0.01937859701372399, 208: 0.0015997907192015133, 209: 7.043103795226788e-05, 210: 1.0061576850323983e-05, 211: 0.00013080049905421178, 212: 0.0003521551897613394, 213: 0.00018110838330583168, 214: 8.049261480259187e-05, 215: 6.03694611019439e-05, 216: 0.0003521551897613394, 217: 0.012295246911095906, 218: 0.0010363424155833702, 219: 0.0002817241518090715, 220: 0.0006338793415704109, 221: 0.0002817241518090715, 222: 5.0307884251619915e-05, 223: 7.043103795226788e-05, 224: 0.00014086207590453576, 225: 0.0192981043989214, 226: 0.0013381897210930898, 227: 0.00016098522960518373, 228: 0.00027166257495874754, 229: 0.0011067734535356382, 230: 0.019066688131363946, 231: 4.0246307401295933e-05, 232: 0.019388658590574315, 233: 8.049261480259187e-05, 234: 1.0061576850323983e-05, 235: 0.0026461947116352075, 236: 0.00010061576850323983, 237: 2.0123153700647967e-05, 238: 3.018473055097195e-05, 239: 0.0008753571859781865, 240: 9.055419165291584e-05, 241: 0.0007143719563730028, 242: 0.00013080049905421178, 243: 0.00018110838330583168, 244: 2.0123153700647967e-05, 245: 5.0307884251619915e-05, 246: 0.00045277095826457925, 247: 1.0061576850323983e-05, 248: 0.0002314162675574516, 249: 0.00032197045921036747, 250: 4.0246307401295933e-05, 251: 1.0061576850323983e-05, 252: 0.008632832937577977, 253: 0.006650702298064153, 254: 0.001690344910854429, 255: 0.0021129311385680366, 256: 0.0006540024952710589, 257: 4.0246307401295933e-05, 258: 2.0123153700647967e-05, 259: 0.0015997907192015133, 260: 0.0003119088823600435, 261: 0.00815993882561275, 262: 1.0061576850323983e-05, 263: 0.00042258622771360727, 264: 0.00027166257495874754, 265: 2.0123153700647967e-05, 266: 5.0307884251619915e-05, 267: 0.0009558498007807783, 268: 0.00032197045921036747, 269: 1.0061576850323983e-05, 270: 1.0061576850323983e-05, 271: 0.018553547711997423, 272: 0.009025234434740613, 273: 0.0003119088823600435, 274: 0.0002314162675574516, 275: 1.0061576850323983e-05, 276: 0.0020324385237654443, 277: 0.007505936330341691, 278: 0.0004427093814142552, 279: 0.00014086207590453576, 280: 0.00026160099810842356, 281: 1.0061576850323983e-05, 282: 2.0123153700647967e-05, 283: 0.0002515394212580996, 284: 0.002394655290377108, 285: 0.0059564534953917975, 286: 0.00026160099810842356, 287: 5.0307884251619915e-05, 288: 0.00042258622771360727, 289: 0.00013080049905421178, 290: 0.0004628325351149032, 291: 1.0061576850323983e-05, 292: 8.049261480259187e-05, 293: 0.0003420936129110154, 294: 5.0307884251619915e-05, 295: 1.0061576850323983e-05, 296: 0.018825210286956173, 297: 0.007214150601682296, 298: 0.010162192618827223, 299: 0.0068318106813699845, 300: 3.018473055097195e-05, 301: 4.0246307401295933e-05, 302: 1.0061576850323983e-05, 303: 0.0016702217571537812, 304: 2.0123153700647967e-05, 305: 0.0026763794421861796, 306: 0.0010162192618827222, 307: 6.03694611019439e-05, 308: 0.00014086207590453576, 309: 0.0013381897210930898, 310: 2.0123153700647967e-05, 311: 0.00038233992031231137, 312: 0.0009558498007807783, 313: 1.0061576850323983e-05, 314: 0.0001207389222038878, 315: 0.0004930172656658751, 316: 5.0307884251619915e-05, 317: 5.0307884251619915e-05, 318: 0.00016098522960518373, 319: 0.0021733005996699803, 320: 0.0001207389222038878, 321: 1.0061576850323983e-05, 322: 0.0005433251499174951, 323: 5.0307884251619915e-05, 324: 0.002465086328329376, 325: 0.0001207389222038878, 326: 7.043103795226788e-05, 327: 0.0003521551897613394, 328: 0.00036221676661166337, 329: 0.00032197045921036747, 330: 0.01033323942528273, 331: 0.00010061576850323983, 332: 0.0001207389222038878, 333: 1.0061576850323983e-05, 334: 0.0006741256489717068, 335: 0.0007143719563730028, 336: 0.00030184730550971946, 337: 0.0004829556888155512, 338: 0.0025053326357306715, 339: 0.00013080049905421178, 340: 0.0002917857286593955, 341: 0.00014086207590453576, 342: 0.00237453213667646, 343: 0.00015092365275485973, 344: 0.015303658389342778, 345: 0.0002515394212580996, 346: 0.0025053326357306715, 347: 0.0013985591821950335, 348: 2.0123153700647967e-05, 349: 2.0123153700647967e-05, 350: 0.019016380247112327, 351: 0.012476355294401739, 352: 1.0061576850323983e-05, 353: 9.055419165291584e-05, 354: 1.0061576850323983e-05, 355: 3.018473055097195e-05, 356: 0.0001106773453535638, 357: 4.0246307401295933e-05, 358: 0.00237453213667646, 359: 0.0004427093814142552, 360: 0.00019116996015615569, 361: 0.011067734535356382, 
                362: 7.043103795226788e-05, 363: 0.0006942488026723549, 364: 2.0123153700647967e-05, 365: 8.049261480259187e-05, 366: 5.0307884251619915e-05, 367: 0.002283977945023544, 368: 6.03694611019439e-05, 369: 0.001066527146134342, 370: 0.0008149877248762426, 371: 0.0001710468064555077, 372: 0.0013482512979434136, 373: 0.0031392119773010828, 374: 8.049261480259187e-05, 375: 0.0027266873264377993, 376: 2.0123153700647967e-05, 377: 0.0010363424155833702, 378: 0.01949933593592788, 379: 0.005775345112085966, 380: 0.0012073892220388779, 381: 2.0123153700647967e-05, 382: 2.0123153700647967e-05, 383: 0.00013080049905421178, 384: 0.0002917857286593955, 385: 0.01720529641405401, 386: 0.0006640640721213829, 387: 0.010142069465126574, 388: 1.0061576850323983e-05, 389: 3.018473055097195e-05, 390: 0.019539582243329176, 391: 0.00019116996015615569, 392: 0.014971626353282086, 393: 0.0004628325351149032, 394: 0.001066527146134342, 395: 0.001157081337787258, 396: 0.00019116996015615569, 397: 2.0123153700647967e-05, 398: 1.0061576850323983e-05, 399: 0.0002817241518090715, 400: 9.055419165291584e-05, 401: 0.0019318227552622047, 402: 0.0001207389222038878, 403: 0.00021129311385680364, 404: 0.002485209482030024, 405: 6.03694611019439e-05, 406: 0.0006439409184207349, 407: 7.043103795226788e-05, 408: 0.0004024630740129593, 409: 0.005453374652875599, 410: 2.0123153700647967e-05, 411: 0.01909687286191492, 412: 0.00036221676661166337, 413: 4.0246307401295933e-05, 414: 1.0061576850323983e-05, 415: 0.0019217611784118807, 416: 6.03694611019439e-05, 417: 0.0004125246508632833, 418: 0.00039240149716263535, 419: 0.0004628325351149032, 420: 1.0061576850323983e-05, 421: 0.0145993480098201, 422: 0.018895641324908438, 423: 0.0001106773453535638, 424: 0.0008250493017265666, 425: 0.00018110838330583168, 426: 1.0061576850323983e-05, 427: 7.043103795226788e-05, 428: 3.018473055097195e-05, 429: 0.00578540668893629, 430: 0.019187427053567834, 431: 8.049261480259187e-05, 432: 4.0246307401295933e-05, 433: 0.00032197045921036747, 434: 0.0001106773453535638, 435: 4.0246307401295933e-05, 436: 0.0005433251499174951, 437: 0.0020324385237654443, 438: 0.00032197045921036747, 439: 3.018473055097195e-05, 440: 0.0013080049905421177, 441: 0.001197327645188554, 442: 0.0014186823358956816, 443: 0.0002917857286593955, 444: 0.005282327846420091, 445: 0.013311466172978629, 446: 2.0123153700647967e-05, 447: 0.00113695818408661, 448: 0.00016098522960518373, 449: 7.043103795226788e-05, 450: 0.0003521551897613394, 451: 0.01919748863041816, 452: 0.003551736628164366, 453: 0.00018110838330583168, 454: 0.00014086207590453576, 455: 0.0007747414174749467, 456: 8.049261480259187e-05, 457: 0.00037227834346198734, 458: 1.0061576850323983e-05, 459: 0.003481305590212098, 460: 0.00019116996015615569, 461: 0.017899545216726365, 462: 0.0002314162675574516, 463: 0.0002515394212580996, 464: 0.0006741256489717068, 465: 0.00019116996015615569, 466: 0.0002817241518090715, 467: 4.0246307401295933e-05, 468: 0.00027166257495874754, 469: 0.01872459451845293, 470: 0.0001106773453535638, 471: 0.00013080049905421178, 472: 0.00013080049905421178, 473: 0.00032197045921036747, 474: 0.017778806294522477, 475: 0.00042258622771360727, 476: 0.0001710468064555077, 477: 0.0012878818368414699, 478: 1.0061576850323983e-05, 479: 9.055419165291584e-05, 480: 0.00010061576850323983, 481: 4.0246307401295933e-05, 482: 9.055419165291584e-05, 483: 0.00018110838330583168, 484: 0.0004125246508632833, 485: 0.0002817241518090715, 486: 3.018473055097195e-05, 487: 0.0006540024952710589, 488: 9.055419165291584e-05, 489: 0.0002314162675574516, 490: 0.0002414778444077756, 491: 1.0061576850323983e-05, 492: 2.0123153700647967e-05, 493: 0.000593633034169115, 494: 0.0004829556888155512, 495: 0.0001710468064555077, 496: 0.018885579748058115, 497: 0.00018110838330583168, 498: 0.019318227552622046, 499: 0.0005030788425161992, 500: 0.019016380247112327, 501: 0.0009960961081820743, 502: 0.015394212580995693, 503: 3.018473055097195e-05, 504: 0.0001106773453535638, 505: 0.00019116996015615569, 506: 3.018473055097195e-05, 507: 0.0006338793415704109, 508: 5.0307884251619915e-05, 509: 6.03694611019439e-05, 510: 0.002344347406125488, 511: 0.0005433251499174951, 512: 0.00036221676661166337, 513: 0.00010061576850323983, 514: 0.001247635529440174, 515: 0.0010162192618827222, 516: 3.018473055097195e-05, 517: 0.0036523523966676056, 518: 3.018473055097195e-05, 519: 0.00030184730550971946, 520: 0.00018110838330583168, 521: 0.0020022537932144727, 522: 0.0009759729544814264, 523: 0.00032197045921036747, 524: 0.0012174507988892019, 525: 0.00016098522960518373, 526: 0.0001207389222038878, 527: 2.0123153700647967e-05, 528: 0.0021531774459693323, 529: 0.0004829556888155512, 530: 0.00026160099810842356, 531: 0.00013080049905421178, 532: 4.0246307401295933e-05, 533: 0.0001106773453535638, 534: 0.00123757395258985, 535: 7.043103795226788e-05, 536: 0.0020123153700647967, 537: 0.0009156034933794824, 538: 9.055419165291584e-05, 539: 0.0007244335332233267, 540: 0.01897613393971103, 541: 4.0246307401295933e-05, 542: 1.0061576850323983e-05, 543: 1.0061576850323983e-05, 544: 0.019438966474825934, 545: 4.0246307401295933e-05, 546: 0.000563448303618143, 547: 0.008592586630176681, 548: 0.007596490521994607, 549: 0.0185434861351471, 550: 0.0009659113776311023, 551: 7.043103795226788e-05, 552: 0.0007445566869239747, 553: 0.0006640640721213829, 554: 0.0005735098804684671, 555: 0.0004427093814142552, 556: 0.019167303899867188, 557: 4.0246307401295933e-05, 558: 0.0009055419165291585, 559: 0.016450678150279712, 560: 8.049261480259187e-05, 561: 5.0307884251619915e-05, 562: 0.0005030788425161992, 563: 0.005483559383426571, 564: 0.00020123153700647966, 565: 0.0019418843321125287, 566: 0.0028675494023423353, 567: 3.018473055097195e-05, 568: 1.0061576850323983e-05, 569: 0.019157242323016865, 570: 0.002958103593995251, 571: 3.018473055097195e-05, 572: 5.0307884251619915e-05, 573: 0.0002414778444077756, 574: 2.0123153700647967e-05, 575: 2.0123153700647967e-05, 576: 0.00016098522960518373, 577: 8.049261480259187e-05, 578: 0.0015494828349498933, 579: 0.0006640640721213829, 580: 1.0061576850323983e-05, 581: 0.0002414778444077756, 582: 1.0061576850323983e-05, 583: 4.0246307401295933e-05, 584: 0.0007546182637742987, 585: 6.03694611019439e-05, 586: 0.0012677586831408218, 587: 0.0002414778444077756, 588: 0.0002213546907071276, 589: 0.018835271863806496, 590: 0.00016098522960518373, 591: 0.004256047007687044, 592: 0.019388658590574315, 593: 0.00038233992031231137, 594: 3.018473055097195e-05, 595: 0.019569766973880146, 596: 0.0003521551897613394, 597: 1.0061576850323983e-05, 598: 0.00013080049905421178, 599: 9.055419165291584e-05, 600: 0.00014086207590453576, 601: 0.00037227834346198734, 602: 3.018473055097195e-05, 603: 4.0246307401295933e-05, 604: 0.0010061576850323984, 605: 0.0019620074858131766, 606: 1.0061576850323983e-05, 607: 0.0001207389222038878, 608: 0.0002414778444077756, 609: 0.004920111079808428, 610: 0.00010061576850323983, 611: 0.016994003300197208, 612: 0.00042258622771360727, 613: 0.0008149877248762426, 614: 0.00899504970418964, 615: 0.0187950255564052, 616: 7.043103795226788e-05, 617: 0.01576649092445768, 618: 0.00010061576850323983, 619: 8.049261480259187e-05, 620: 7.043103795226788e-05, 621: 0.006328731838853785, 622: 0.0008954803396788345, 623: 0.00015092365275485973, 624: 0.0013482512979434136, 625: 4.0246307401295933e-05, 626: 0.018130961484283815, 627: 0.0059866382259427696, 628: 0.01217450798889202, 629: 0.0004829556888155512, 630: 0.004205739123435425, 631: 0.018573670865698073, 632: 5.0307884251619915e-05, 633: 0.0022236084839216, 634: 0.00010061576850323983, 635: 7.043103795226788e-05, 636: 0.0007646798406246227, 637: 0.0002213546907071276, 638: 0.00010061576850323983, 639: 0.0034309977059604783, 640: 1.0061576850323983e-05, 641: 0.0070934116794784076, 642: 0.01911699601561557, 643: 0.0018915764478609088, 644: 0.002283977945023544, 645: 0.00866301766812895, 646: 6.03694611019439e-05, 647: 0.001157081337787258, 648: 5.0307884251619915e-05, 649: 0.00043264780456393125, 650: 0.00014086207590453576, 651: 6.03694611019439e-05, 652: 0.00015092365275485973, 653: 0.011741860184328087, 654: 0.00015092365275485973, 655: 0.010001207389222038, 656: 0.0002817241518090715, 657: 2.0123153700647967e-05, 658: 0.0034008129754095062, 659: 0.00336056666800821, 660: 0.00015092365275485973, 661: 0.0010262808387330462, 662: 1.0061576850323983e-05, 663: 8.049261480259187e-05, 664: 1.0061576850323983e-05, 665: 1.0061576850323983e-05, 666: 0.0002817241518090715, 667: 0.0009156034933794824, 668: 0.0026160099810842354, 669: 0.0013180665673924417, 670: 0.0012878818368414699, 671: 3.018473055097195e-05, 672: 0.00010061576850323983, 673: 0.00039240149716263535, 674: 2.0123153700647967e-05, 675: 0.009306958586549684, 676: 0.00356179820501469, 677: 0.0003119088823600435, 678: 0.00010061576850323983, 679: 0.0021632390228196563, 680: 0.0007143719563730028, 681: 0.00043264780456393125, 682: 0.0002817241518090715, 683: 0.0003420936129110154, 684: 0.00016098522960518373, 685: 0.0027468104801384474, 686: 2.0123153700647967e-05, 687: 0.010051515273473659, 688: 4.0246307401295933e-05, 689: 0.0010765887229846661, 690: 2.0123153700647967e-05, 691: 1.0061576850323983e-05, 692: 0.004567955890047088, 693: 9.055419165291584e-05, 694: 0.01912705759246589, 695: 0.000613756187869763, 696: 0.0009156034933794824, 697: 0.0004628325351149032, 698: 0.014106330744154225, 699: 0.0007445566869239747, 700: 0.004235923853986397, 701: 0.00227391636817322, 702: 0.00010061576850323983, 703: 0.01730591218255725, 704: 0.0026260715579345595, 705: 0.013452328248883165, 706: 0.002948042017144927, 707: 0.000623817764720087, 708: 0.008190123556163722, 709: 0.004095061778081861, 710: 6.03694611019439e-05, 711: 0.00016098522960518373, 712: 7.043103795226788e-05, 713: 0.018020284138930254, 714: 2.0123153700647967e-05, 715: 0.019267919668370426, 716: 0.019519459089628526, 717: 0.00013080049905421178, 718: 0.00016098522960518373, 719: 0.004507586428945144, 
                720: 0.0001207389222038878, 721: 0.00018110838330583168, 722: 4.0246307401295933e-05, 723: 0.0007143719563730028, 724: 0.0019016380247112328, 725: 0.0007445566869239747, 726: 0.0010363424155833702, 727: 3.018473055097195e-05, 728: 0.0011067734535356382, 729: 5.0307884251619915e-05, 730: 9.055419165291584e-05, 731: 0.00015092365275485973, 732: 0.0002515394212580996, 733: 7.043103795226788e-05, 734: 0.0065098402221596165, 735: 0.008290739324666962, 736: 0.0010061576850323984, 737: 0.0004728941119652272, 738: 0.0012174507988892019, 739: 0.0073550126775868314, 740: 0.0007344951100736508, 741: 0.0004930172656658751, 742: 0.0002314162675574516, 743: 4.0246307401295933e-05, 744: 0.000623817764720087, 745: 0.00118726606833823, 746: 0.0009960961081820743, 747: 0.0004024630740129593, 748: 0.010423793616935646, 749: 0.0010564655692840183, 750: 0.00021129311385680364, 751: 0.0023041010987241923, 752: 7.043103795226788e-05, 753: 0.0007244335332233267, 754: 9.055419165291584e-05, 755: 0.00010061576850323983, 756: 0.0033001972069062664, 757: 0.004708817965951624, 758: 1.0061576850323983e-05, 759: 2.0123153700647967e-05, 760: 0.0002414778444077756, 761: 0.0001106773453535638, 762: 0.0007244335332233267, 763: 0.0001710468064555077, 764: 0.00010061576850323983, 765: 0.0001710468064555077, 766: 0.004920111079808428, 767: 0.0004728941119652272, 768: 0.0009357266470801304, 769: 1.0061576850323983e-05, 770: 0.0001207389222038878, 771: 0.0022940395218738682, 772: 5.0307884251619915e-05, 773: 0.0007646798406246227, 774: 0.0007143719563730028, 775: 0.00030184730550971946, 776: 0.0010967118766853142, 777: 0.00014086207590453576, 778: 1.0061576850323983e-05, 779: 8.049261480259187e-05, 780: 0.00026160099810842356, 781: 0.0034108745522598303, 782: 0.0015394212580995693, 783: 2.0123153700647967e-05, 784: 3.018473055097195e-05, 785: 8.049261480259187e-05, 786: 0.0004728941119652272, 787: 8.049261480259187e-05, 788: 6.03694611019439e-05, 789: 0.001167142914637582, 790: 0.0006942488026723549, 791: 0.00013080049905421178, 792: 6.03694611019439e-05, 793: 0.019398720167424638, 794: 0.0007948645711755946, 795: 0.00014086207590453576, 796: 7.043103795226788e-05, 797: 0.00020123153700647966, 798: 0.0011470197609369341, 799: 0.0022236084839216, 800: 0.005050911578862639, 801: 0.000623817764720087, 802: 0.0008753571859781865, 803: 4.0246307401295933e-05, 804: 0.019237734937819453, 805: 7.043103795226788e-05, 806: 0.0018714532941602607, 807: 0.00018110838330583168, 808: 4.0246307401295933e-05, 809: 3.018473055097195e-05, 810: 7.043103795226788e-05, 811: 0.014217008089507788, 812: 7.043103795226788e-05, 813: 0.0001207389222038878, 814: 0.002495271058880348, 815: 4.0246307401295933e-05, 816: 0.01892582605545941, 817: 0.005322574153821387, 818: 0.019438966474825934, 819: 0.0005030788425161992, 820: 3.018473055097195e-05, 821: 0.0002817241518090715, 822: 0.00010061576850323983, 823: 5.0307884251619915e-05, 824: 5.0307884251619915e-05, 825: 0.00018110838330583168, 826: 0.00014086207590453576, 827: 0.019016380247112327, 828: 0.006640640721213829, 829: 8.049261480259187e-05, 830: 0.0002515394212580996, 831: 0.00010061576850323983, 832: 0.0021934237533706284, 833: 0.00036221676661166337, 834: 0.00016098522960518373, 835: 0.008129754095061777, 836: 0.004819495311305187, 837: 0.014126453897854873, 838: 0.0030889040930494626, 839: 0.0005735098804684671, 840: 0.0002817241518090715, 841: 0.0008049261480259186, 842: 0.0001106773453535638, 843: 0.0009256650702298064, 844: 0.00020123153700647966, 845: 0.0019519459089628528, 846: 0.0002314162675574516, 847: 0.0005232019962168471, 848: 0.00677144122026804, 849: 2.0123153700647967e-05, 850: 2.0123153700647967e-05, 851: 1.0061576850323983e-05, 852: 2.0123153700647967e-05, 853: 8.049261480259187e-05, 854: 0.0008049261480259186, 855: 3.018473055097195e-05, 856: 3.018473055097195e-05, 857: 0.00015092365275485973, 858: 2.0123153700647967e-05, 859: 0.00010061576850323983, 860: 0.0038837686642250575, 861: 0.0006540024952710589, 862: 3.018473055097195e-05, 863: 0.0003521551897613394, 864: 5.0307884251619915e-05, 865: 0.001811083833058317, 866: 0.0009960961081820743, 867: 0.0004930172656658751, 868: 0.0002817241518090715, 869: 1.0061576850323983e-05, 870: 0.0005232019962168471, 871: 0.00036221676661166337, 872: 0.0007043103795226788, 873: 6.03694611019439e-05, 874: 0.0002917857286593955, 875: 0.0002414778444077756, 876: 0.011218658188111241, 877: 0.000613756187869763, 878: 0.00018110838330583168, 879: 0.00018110838330583168, 880: 0.006690948605465448, 881: 0.011027488227955086, 882: 0.0002917857286593955, 883: 8.049261480259187e-05, 884: 0.00014086207590453576, 885: 0.0163198776512255, 886: 2.0123153700647967e-05, 887: 3.018473055097195e-05, 888: 0.00032197045921036747, 889: 0.0005533867267678191, 890: 1.0061576850323983e-05, 891: 0.00010061576850323983, 892: 0.00010061576850323983, 893: 0.0004728941119652272, 894: 3.018473055097195e-05, 895: 0.0002917857286593955, 896: 0.003441059282810802, 897: 0.0002515394212580996, 898: 0.01478045639312593, 899: 0.005242081539018795, 900: 0.003491367167062422, 901: 0.0003521551897613394, 902: 7.043103795226788e-05, 903: 0.0020827464080170645, 904: 0.0010866502998349902, 905: 2.0123153700647967e-05, 906: 0.0003420936129110154, 907: 0.0001207389222038878, 908: 0.00010061576850323983, 909: 0.00013080049905421178, 910: 0.0036322292429669576, 911: 0.010292993117881434, 912: 0.007868153096953354, 913: 2.0123153700647967e-05, 914: 5.0307884251619915e-05, 915: 0.002485209482030024, 916: 0.0022236084839216, 917: 4.0246307401295933e-05, 918: 8.049261480259187e-05, 919: 0.0015897291423511892, 920: 3.018473055097195e-05, 921: 0.007566305791443635, 922: 0.0006439409184207349, 923: 0.007113534833179056, 924: 0.0014388054895963294, 925: 1.0061576850323983e-05, 926: 0.0004930172656658751, 927: 0.0012677586831408218, 928: 0.0007646798406246227, 929: 0.0001106773453535638, 930: 0.0001106773453535638, 931: 4.0246307401295933e-05, 932: 0.00039240149716263535, 933: 0.0001106773453535638, 934: 0.00013080049905421178, 935: 0.0009156034933794824, 936: 0.00014086207590453576, 937: 5.0307884251619915e-05, 938: 3.018473055097195e-05, 939: 0.00010061576850323983, 940: 0.00018110838330583168, 941: 9.055419165291584e-05, 942: 6.03694611019439e-05, 943: 0.009568559584658107, 944: 2.0123153700647967e-05, 945: 1.0061576850323983e-05, 946: 0.00019116996015615569, 947: 0.019539582243329176, 948: 0.01927798124522075, 949: 0.0013985591821950335, 950: 0.00043264780456393125, 951: 0.019811244818287922, 952: 5.0307884251619915e-05, 953: 0.0013482512979434136, 954: 0.0007445566869239747, 955: 0.0038334607799734373, 956: 1.0061576850323983e-05, 957: 0.0038334607799734373, 958: 6.03694611019439e-05, 959: 0.018372439328691592, 960: 0.0002817241518090715, 961: 0.016450678150279712, 962: 0.019791121664587272, 963: 0.00016098522960518373, 964: 0.01937859701372399, 965: 0.018000160985229604, 966: 0.0040346923169799175, 967: 0.0198011832414376, 968: 0.011741860184328087, 969: 1.0061576850323983e-05, 970: 0.000563448303618143, 971: 0.0001710468064555077, 972: 1.0061576850323983e-05, 973: 0.000583571457318791, 974: 9.055419165291584e-05, 975: 8.049261480259187e-05, 976: 0.011309212379764157, 977: 0.0003119088823600435, 978: 0.00016098522960518373, 979: 0.004940234233509076, 980: 0.004346601199339961, 981: 0.019569766973880146, 982: 0.019106934438765242, 983: 5.0307884251619915e-05, 984: 0.0002817241518090715, 985: 7.043103795226788e-05, 986: 0.0014689902201473015, 987: 1.0061576850323983e-05, 988: 0.0002515394212580996, 989: 0.0002213546907071276, 990: 1.0061576850323983e-05, 991: 0.00010061576850323983, 992: 9.055419165291584e-05, 993: 0.0030989656698997867, 994: 4.0246307401295933e-05, 995: 0.019811244818287922, 996: 0.00045277095826457925, 997: 0.0001207389222038878, 998: 1.0061576850323983e-05, 999: 0.0008552340322775386, 1000: 0.011339397110315128, 1001: 0.0001106773453535638, 1002: 0.0006036946110194389, 1003: 1.0061576850323983e-05, 1004: 0.00016098522960518373, 1005: 1.0061576850323983e-05, 1006: 0.0006540024952710589, 1007: 0.00013080049905421178, 1008: 0.006590332836962209, 1009: 0.0005131404193665231, 1010: 1.0061576850323983e-05, 1011: 0.00677144122026804, 1012: 5.0307884251619915e-05, 1013: 0.00036221676661166337, 1014: 0.0005433251499174951, 1015: 5.0307884251619915e-05, 1016: 8.049261480259187e-05, 1017: 0.0030687809393488146, 1018: 0.0029882883245462228, 1019: 0.010594840423391153, 1020: 0.002243731637622248, 1021: 0.01043385519378597, 1022: 0.0006338793415704109, 1023: 0.01892582605545941, 1024: 0.005101219463114259, 1025: 0.003350505091157886, 1026: 0.01922767336096913, 1027: 0.01775868314082183, 1028: 1.0061576850323983e-05, 1029: 5.0307884251619915e-05, 1030: 1.0061576850323983e-05, 1031: 9.055419165291584e-05, 1032: 2.0123153700647967e-05, 1033: 0.0015192981043989215, 1034: 0.0008250493017265666, 1035: 0.01942890489797561, 1036: 0.0004125246508632833, 1037: 0.019066688131363946, 1038: 0.0002414778444077756, 1039: 0.0002213546907071276, 1040: 0.0003521551897613394, 1041: 0.0006942488026723549, 1042: 0.009679236930011671, 1043: 0.005916207187990502, 1044: 0.00021129311385680364, 1045: 0.008300800901517285, 1046: 0.0005232019962168471, 1047: 5.0307884251619915e-05, 1048: 5.0307884251619915e-05, 1049: 5.0307884251619915e-05, 1050: 0.018714532941602607, 1051: 0.000563448303618143, 1052: 0.01591741457721254, 1053: 7.043103795226788e-05, 1054: 2.0123153700647967e-05, 1055: 0.015716183040206062, 1056: 0.018966072362860707, 1057: 1.0061576850323983e-05, 1058: 5.0307884251619915e-05, 1059: 0.0013784360284943857, 1060: 0.011007365074254438, 1061: 0.0013482512979434136, 1062: 0.0002917857286593955, 1063: 0.0002213546907071276, 1064: 0.005252143115869119, 1065: 0.0005030788425161992, 1066: 0.0006841872258220308, 1067: 0.00016098522960518373, 1068: 0.0004024630740129593, 1069: 0.0003521551897613394, 1070: 0.0013583128747937376, 1071: 0.014217008089507788, 1072: 0.007767537328450115, 1073: 0.0005030788425161992, 1074: 0.010212500503078842, 
                1075: 1.0061576850323983e-05, 1076: 0.0006540024952710589, 1077: 0.01911699601561557, 1078: 0.013100173059121825, 1079: 0.019891737433090514, 1080: 2.0123153700647967e-05, 1081: 0.0002917857286593955, 1082: 0.00036221676661166337, 1083: 0.0013884976053447097, 1084: 4.0246307401295933e-05, 1085: 0.0006741256489717068, 1086: 0.00026160099810842356, 1087: 0.0002515394212580996, 1088: 0.00033203203606069144, 1089: 0.00037227834346198734, 1090: 0.0005030788425161992, 1091: 0.0027166257495874753, 1092: 0.0001207389222038878, 1093: 0.0031794582847023784, 1094: 0.0004125246508632833, 1095: 0.002253793214472572, 1096: 0.0010564655692840183, 1097: 0.01936853543687367, 1098: 0.010272869964180786, 1099: 0.012204692719442991, 1100: 0.001730591218255725, 1101: 0.0002817241518090715, 1102: 0.007495874753491367, 1103: 0.0018815148710105848, 1104: 0.0014790517969976256, 1105: 0.0013683744516440617, 1106: 0.0003420936129110154, 1107: 0.0004125246508632833, 1108: 0.006399162876806053, 1109: 0.0057350988046846706, 1110: 0.011560751801022257, 1111: 0.000613756187869763, 1112: 0.019016380247112327, 1113: 0.00018110838330583168, 1114: 0.0014388054895963294, 1115: 0.015263412081941482, 1116: 7.043103795226788e-05, 1117: 0.009488066969855515, 1118: 6.03694611019439e-05, 1119: 1.0061576850323983e-05, 1120: 0.0001106773453535638, 1121: 0.0010162192618827222, 1122: 0.019207550207268484, 1123: 0.008049261480259187, 1124: 1.0061576850323983e-05, 1125: 0.0004427093814142552, 1126: 3.018473055097195e-05, 1127: 0.0004427093814142552, 1128: 0.0003119088823600435, 1129: 7.043103795226788e-05, 1130: 0.00020123153700647966, 1131: 0.0001106773453535638, 1132: 0.00013080049905421178, 1133: 0.019358473860023342, 1134: 0.00113695818408661, 1135: 2.0123153700647967e-05, 1136: 0.0013985591821950335, 1137: 0.0001207389222038878, 1138: 0.00037227834346198734, 1139: 0.018774902402704553, 1140: 0.0004728941119652272, 1141: 0.014770394816275607, 1142: 0.007334889523886183, 1143: 0.0002414778444077756, 1144: 1.0061576850323983e-05, 1145: 0.00010061576850323983, 1146: 3.018473055097195e-05, 1147: 0.00014086207590453576, 1148: 4.0246307401295933e-05, 1149: 0.0002917857286593955, 1150: 4.0246307401295933e-05, 1151: 0.0007043103795226788, 1152: 0.0004628325351149032, 1153: 0.00014086207590453576, 1154: 0.0004829556888155512, 1155: 0.018664225057350988, 1156: 0.00113695818408661, 1157: 1.0061576850323983e-05, 1158: 1.0061576850323983e-05, 1159: 0.00010061576850323983, 1160: 0.0005433251499174951, 1161: 0.01934841228317302, 1162: 0.006338793415704109, 1163: 0.0003119088823600435, 1164: 0.0006942488026723549, 1165: 7.043103795226788e-05, 1166: 0.0001106773453535638, 1167: 1.0061576850323983e-05, 1168: 0.00030184730550971946, 1169: 0.0005030788425161992, 1170: 0.00045277095826457925, 1171: 0.0002817241518090715, 1172: 0.0011470197609369341, 1173: 0.0019418843321125287, 1174: 0.00021129311385680364, 1175: 0.0009156034933794824, 1176: 0.0003119088823600435, 1177: 0.01478045639312593, 1178: 0.019358473860023342, 1179: 0.0008753571859781865, 1180: 0.0007747414174749467, 1181: 0.0001106773453535638, 1182: 0.0004728941119652272, 1183: 0.00021129311385680364, 1184: 0.0004728941119652272, 1185: 0.0007043103795226788, 1186: 0.01849317825089548, 1187: 0.00019116996015615569, 1188: 0.005342697307522035, 1189: 0.0001106773453535638, 1190: 0.009467943816154867, 1191: 0.00113695818408661, 1192: 0.00026160099810842356, 1193: 5.0307884251619915e-05, 1194: 0.000563448303618143, 1195: 0.0007344951100736508, 1196: 0.00032197045921036747, 1197: 0.0012878818368414699, 1198: 0.0062683623777518415, 1199: 0.0001207389222038878, 1200: 0.0005232019962168471, 1201: 0.0001106773453535638, 1202: 0.016843079647442347, 1203: 0.0008149877248762426}

Frequency_list = [64, 364, 1911, 149, 29, 26, 59, 22, 12, 28, 505, 1207, 4, 10, 500, 33, 3, 44, 561, 8, 9, 33, 1883, 98, 70, 46, 117, 41, 1395, 7, 1, 314, 31, 1905, 1859, 1623, 47, 3, 3, 1, 305, 6, 210, 36, 1787, 17, 51, 138, 3, 1470, 3, 2, 186, 76, 26, 303, 738, 1799, 1934, 1609, 1622, 41, 4, 11, 270, 349, 42, 823, 6, 48, 3, 42, 24, 16, 605, 646, 1765, 2, 125, 1420, 140, 4, 322, 60, 2, 231, 333, 1941, 367, 1922, 18, 81, 1, 1852, 430, 247, 94, 21, 1821, 16, 12, 25, 41, 244, 7, 1, 40, 40, 104, 1671, 49, 243, 2, 242, 271, 104, 8, 1758, 1, 48, 14, 40, 1, 37, 1510, 6, 1903, 70, 86, 7, 5, 1406, 1901, 15, 28, 6, 494, 234, 1922, 1, 35, 5, 1828, 8, 63, 1668, 4, 95, 17, 1567, 2, 103, 50, 1309, 6, 92, 19, 37, 4, 709, 9, 82, 15, 3, 61, 51, 5, 13, 642, 24, 255, 9, 1808, 31, 158, 80, 1884, 158, 2, 12, 1659, 7, 834, 57, 174, 95, 27, 22, 1391, 90, 40, 445, 21, 1132, 177, 4, 17, 84, 55, 30, 25, 2, 125, 1135, 19, 72, 1926, 159, 7, 1, 13, 35, 18, 8, 6, 35, 1222, 103, 28, 63, 28, 5, 7, 14, 1918, 133, 16, 27, 110, 1895, 4, 1927, 8, 1, 263, 10, 2, 3, 87, 9, 71, 13, 18, 2, 5, 45, 1, 23, 32, 4, 1, 858, 661, 168, 210, 65, 4, 2, 159, 31, 811, 1, 42, 27, 2, 5, 95, 32, 1, 1, 1844, 897, 31, 23, 1, 202, 746, 44, 14, 26, 1, 2, 25, 238, 592, 26, 5, 42, 13, 46, 1, 8, 34, 5, 1, 1871, 717, 1010, 679, 3, 4, 1, 166, 2, 266, 101, 6, 14, 133, 2, 38, 95, 1, 12, 49, 5, 5, 16, 216, 12, 1, 54, 5, 245, 12, 7, 35, 36, 32, 1027, 10, 12, 1, 67, 71, 30, 48, 249, 13, 29, 14, 236, 15, 1521, 25, 249, 139, 2, 2, 1890, 1240, 1, 9, 1, 3, 11, 4, 236, 44, 19, 1100, 7, 69, 2, 8, 5, 227, 6, 106, 81, 17, 134, 312, 8, 271, 2, 103, 1938, 574, 120, 2, 2, 13, 29, 1710, 66, 1008, 1, 3, 1942, 19, 1488, 46, 106, 115, 19, 2, 1, 28, 9, 192, 12, 21, 247, 6, 64, 7, 40, 542, 2, 1898, 36, 4, 1, 191, 6, 41, 39, 46, 1, 1451, 1878, 11, 82, 18, 1, 7, 3, 575, 1907, 8, 4, 32, 11, 4, 54, 202, 32, 3, 130, 119, 141, 29, 525, 1323, 2, 113, 16, 7, 35, 1908, 353, 18, 14, 77, 8, 37, 1, 346, 19, 1779, 23, 25, 67, 19, 28, 4, 27, 1861, 11, 13, 13, 32, 1767, 42, 17, 128, 1, 9, 10, 4, 9, 18, 41, 28, 3, 65, 9, 23, 24, 1, 2, 59, 48, 17, 1877, 18, 1920, 50, 1890, 99, 1530, 3, 11, 19, 3, 63, 5, 6, 233, 54, 36, 10, 124, 101, 3, 363, 3, 30, 18, 199, 97, 32, 121, 16, 12, 2, 214, 48, 26, 13, 4, 11, 123, 7, 200, 91, 9, 72, 1886, 4, 1, 1, 1932, 4, 56, 854, 755, 1843, 96, 7, 74, 66, 57, 44, 1905, 4, 90, 1635, 8, 5, 50, 545, 20, 193, 285, 3, 1, 1904, 294, 3, 5, 24, 2, 2, 16, 8, 154, 66, 1, 24, 1, 4, 75, 6, 126, 24, 22, 1872, 16, 423, 1927, 38, 3, 1945, 35, 1, 13, 9, 14, 37, 3, 4, 100, 195, 1, 12, 24, 489, 10, 1689, 42, 81, 894, 1868, 7, 1567, 10, 8, 7, 629, 89, 15, 134, 4, 1802, 595, 1210, 48, 418, 1846, 5, 221, 10, 7, 76, 22, 10, 341, 1, 705, 1900, 188, 227, 861, 6, 115, 5, 43, 14, 6, 15, 1167, 15, 994, 28, 2, 338, 334, 15, 102, 1, 8, 1, 1, 28, 91, 260, 131, 128, 3, 10, 39, 2, 925, 354, 31, 10, 215, 71, 43, 28, 34, 16, 273, 2, 999, 4, 107, 2, 1, 454, 9, 1901, 61, 91, 46, 1402, 74, 421, 226, 10, 1720, 261, 1337, 293, 62, 814, 407, 6, 16, 7, 1791, 2, 1915, 1940, 13, 16, 448, 12, 18, 4, 71, 189, 74, 103, 3, 110, 5, 9, 15, 25, 7, 647, 824, 100, 47, 121, 731, 73, 49, 23, 4, 62, 118, 99, 40, 1036, 105, 21, 229, 7, 72, 9, 10, 328, 468, 1, 2, 24, 11, 72, 17, 10, 17, 489, 47, 93, 1, 12, 228, 5, 76, 71, 30, 109, 14, 1, 8, 26, 339, 153, 2, 3, 8, 47, 8, 6, 116, 69, 13, 6, 1928, 79, 14, 7, 20, 114, 221, 502, 62, 87, 4, 1912, 7, 186, 18, 4, 3, 7, 1413, 7, 12, 248, 4, 1881, 529, 1932, 50, 3, 28, 10, 5, 5, 18, 14, 1890, 660, 8, 25, 10, 218, 36, 16, 808, 479, 1404, 307, 57, 28, 80, 11, 92, 20, 194, 23, 52, 673, 2, 2, 1, 2, 8, 80, 3, 3, 15, 2, 10, 386, 65, 3, 35, 5, 180, 99, 49, 28, 1, 52, 36, 70, 6, 29, 24, 1115, 61, 18, 18, 665, 1096, 29, 8, 14, 1622, 2, 3, 32, 55, 1, 10, 10, 47, 3, 29, 342, 25, 1469, 521, 347, 35, 7, 207, 108, 2, 34, 12, 10, 13, 361, 1023, 782, 2, 5, 247, 221, 4, 8, 158, 3, 752, 64, 707, 143, 1, 49, 126, 76, 11, 11, 4, 39, 11, 13, 91, 14, 5, 3, 10, 18, 9, 6, 951, 2, 1, 19, 1942, 1916, 139, 43, 1969, 5, 134, 74, 381, 1, 381, 6, 1826, 28, 1635, 1967, 16, 1926, 1789, 401, 1968, 1167, 1, 56, 17, 1, 58, 9, 8, 1124, 31, 16, 491, 432, 1945, 1899, 5, 28, 7, 146, 1, 25, 22, 1, 10, 9, 308, 4, 1969, 45, 12, 1, 85, 1127, 11, 60, 1, 16, 1, 65, 13, 655, 51, 1, 673, 5, 36, 54, 5, 8, 305, 297, 1053, 223, 1037, 63, 1881, 507, 333, 1911, 1765, 1, 5, 1, 9, 2, 151, 82, 1931, 41, 1895, 24, 22, 35, 69, 962, 588, 21, 825, 52, 5, 5, 5, 1860, 56, 1582, 7, 2, 1562, 1885, 1, 5, 137, 1094, 134, 29, 22, 522, 50, 68, 16, 40, 35, 135, 1413, 772, 50, 1015, 1, 65, 1900, 1302, 1977, 2, 29, 36, 138, 4, 67, 26, 25, 33, 37, 50, 270, 12, 316, 41, 224, 105, 1925, 1021, 1213, 172, 28, 745, 187, 147, 136, 34, 41, 636, 570, 1149, 61, 1890, 18, 143, 1517, 7, 943, 6, 1, 11, 101, 1909, 800, 1, 44, 3, 44, 31, 7, 20, 11, 13, 1924, 113, 2, 139, 12, 37, 1866, 47, 1468, 729, 24, 1, 10, 3, 14, 4, 29, 4, 70, 46, 14, 48, 1855, 113, 1, 1, 10, 54, 1923, 630, 31, 69, 7, 11, 1, 30, 50, 45, 28, 114, 193, 21, 91, 31, 1469, 1924, 87, 77, 11, 47, 21, 47, 70, 1838, 19, 531, 11, 941, 113, 26, 5, 56, 73, 32, 128, 623, 12, 52, 11, 1674, 81]
Frequency_list_no_rare = [64, 364, 1911, 149, 29, 26, 59, 22, 12, 28, 505, 1207, 0, 0, 500, 33, 0, 44, 561, 0, 0, 33, 1883, 98, 70, 46, 117, 41, 1395, 0, 0, 314, 31, 1905, 1859, 1623, 47, 0, 0, 0, 305, 0, 210, 36, 1787, 17, 51, 138, 0, 1470, 0, 0, 186, 76, 26, 303, 738, 1799, 1934, 1609, 1622, 41, 0, 11, 270, 349, 42, 823, 0, 48, 0, 42, 24, 16, 605, 646, 1765, 0, 125, 1420, 140, 0, 322, 60, 0, 231, 333, 1941, 367, 1922, 18, 81, 0, 1852, 430, 247, 94, 21, 1821, 16, 12, 25, 41, 244, 0, 0, 40, 40, 104, 1671, 49, 243, 0, 242, 271, 104, 0, 1758, 0, 48, 14, 40, 0, 37, 1510, 0, 1903, 70, 86, 0, 0, 1406, 1901, 15, 28, 0, 494, 234, 1922, 0, 35, 0, 1828, 0, 63, 1668, 0, 95, 17, 1567, 0, 103, 50, 1309, 0, 92, 19, 37, 0, 709, 0, 82, 15, 0, 61, 51, 0, 13, 642, 24, 255, 0, 1808, 31, 158, 80, 1884, 158, 0, 12, 1659, 0, 834, 57, 174, 95, 27, 22, 1391, 90, 40, 445, 21, 1132, 177, 0, 17, 84, 55, 30, 25, 0, 125, 1135, 19, 72, 1926, 159, 0, 0, 13, 35, 18, 0, 0, 35, 1222, 103, 28, 63, 28, 0, 0, 14, 1918, 133, 16, 27, 110, 1895, 0, 1927, 0, 0, 263, 0, 0, 0, 87, 0, 71, 13, 18, 0, 0, 45, 0, 23, 32, 0, 0, 858, 661, 168, 210, 65, 0, 0, 159, 31, 811, 0, 42, 27, 0, 0, 95, 32, 0, 0, 1844, 897, 31, 23, 0, 202, 746, 44, 14, 26, 0, 0, 25, 238, 592, 26, 0, 42, 13, 46, 0, 0, 34, 0, 0, 1871, 717, 1010, 679, 0, 0, 0, 166, 0, 266, 101, 0, 14, 133, 0, 38, 95, 0, 12, 49, 0, 0, 16, 216, 12, 0, 54, 0, 245, 12, 0, 35, 36, 32, 1027, 0, 12, 0, 67, 71, 30, 48, 249, 13, 29, 14, 236, 15, 1521, 25, 249, 139, 0, 0, 1890, 1240, 0, 0, 0, 0, 11, 0, 236, 44, 19, 1100, 0, 69, 0, 0, 0, 227, 0, 106, 81, 17, 134, 312, 0, 271, 0, 103, 1938, 574, 120, 0, 0, 13, 29, 1710, 66, 1008, 0, 0, 1942, 19, 1488, 46, 106, 115, 19, 0, 0, 28, 0, 192, 12, 21, 247, 0, 64, 0, 40, 542, 0, 1898, 36, 0, 0, 191, 0, 41, 39, 46, 0, 1451, 1878, 11, 82, 18, 0, 0, 0, 575, 1907, 0, 0, 32, 11, 0, 54, 202, 32, 0, 130, 119, 141, 29, 525, 1323, 0, 113, 16, 0, 35, 1908, 353, 18, 14, 77, 0, 37, 0, 346, 19, 1779, 23, 25, 67, 19, 28, 0, 27, 1861, 11, 13, 13, 32, 1767, 42, 17, 128, 0, 0, 0, 0, 0, 18, 41, 28, 0, 65, 0, 23, 24, 0, 0, 59, 48, 17, 1877, 18, 1920, 50, 1890, 99, 1530, 0, 11, 19, 0, 63, 0, 0, 233, 54, 36, 0, 124, 101, 0, 363, 0, 30, 18, 199, 97, 32, 121, 16, 12, 0, 214, 48, 26, 13, 0, 11, 123, 0, 200, 91, 0, 72, 1886, 0, 0, 0, 1932, 0, 56, 854, 755, 1843, 96, 0, 74, 66, 57, 44, 1905, 0, 90, 1635, 0, 0, 50, 545, 20, 193, 285, 0, 0, 1904, 294, 0, 0, 24, 0, 0, 16, 0, 154, 66, 0, 24, 0, 0, 75, 0, 126, 24, 22, 1872, 16, 423, 1927, 38, 0, 1945, 35, 0, 13, 0, 14, 37, 0, 0, 100, 195, 0, 12, 24, 489, 0, 1689, 42, 81, 894, 1868, 0, 1567, 0, 0, 0, 629, 89, 15, 134, 0, 1802, 595, 1210, 48, 418, 1846, 0, 221, 0, 0, 76, 22, 0, 341, 0, 705, 1900, 188, 227, 861, 0, 115, 0, 43, 14, 0, 15, 1167, 15, 994, 28, 0, 338, 334, 15, 102, 0, 0, 0, 0, 28, 91, 260, 131, 128, 0, 0, 39, 0, 925, 354, 31, 0, 215, 71, 43, 28, 34, 16, 273, 0, 999, 0, 107, 0, 0, 454, 0, 1901, 61, 91, 46, 1402, 74, 421, 226, 0, 1720, 261, 1337, 293, 62, 814, 407, 0, 16, 0, 1791, 0, 1915, 1940, 13, 16, 448, 12, 18, 0, 71, 189, 74, 103, 0, 110, 0, 0, 15, 25, 0, 647, 824, 100, 47, 121, 731, 73, 49, 23, 0, 62, 118, 99, 40, 1036, 105, 21, 229, 0, 72, 0, 0, 328, 468, 0, 0, 24, 11, 72, 17, 0, 17, 489, 47, 93, 0, 12, 228, 0, 76, 71, 30, 109, 14, 0, 0, 26, 339, 153, 0, 0, 0, 47, 0, 0, 116, 69, 13, 0, 1928, 79, 14, 0, 20, 114, 221, 502, 62, 87, 0, 1912, 0, 186, 18, 0, 0, 0, 1413, 0, 12, 248, 0, 1881, 529, 1932, 50, 0, 28, 0, 0, 0, 18, 14, 1890, 660, 0, 25, 0, 218, 36, 16, 808, 479, 1404, 307, 57, 28, 80, 11, 92, 20, 194, 23, 52, 673, 0, 0, 0, 0, 0, 80, 0, 0, 15, 0, 0, 386, 65, 0, 35, 0, 180, 99, 49, 28, 0, 52, 36, 70, 0, 29, 24, 1115, 61, 18, 18, 665, 1096, 29, 0, 14, 1622, 0, 0, 32, 55, 0, 0, 0, 47, 0, 29, 342, 25, 1469, 521, 347, 35, 0, 207, 108, 0, 34, 12, 0, 13, 361, 1023, 782, 0, 0, 247, 221, 0, 0, 158, 0, 752, 64, 707, 143, 0, 49, 126, 76, 11, 11, 0, 39, 11, 13, 91, 14, 0, 0, 0, 18, 0, 0, 951, 0, 0, 19, 1942, 1916, 139, 43, 1969, 0, 134, 74, 381, 0, 381, 0, 1826, 28, 1635, 1967, 16, 1926, 1789, 401, 1968, 1167, 0, 56, 17, 0, 58, 0, 0, 1124, 31, 16, 491, 432, 1945, 1899, 0, 28, 0, 146, 0, 25, 22, 0, 0, 0, 308, 0, 1969, 45, 12, 0, 85, 1127, 11, 60, 0, 16, 0, 65, 13, 655, 51, 0, 673, 0, 36, 54, 0, 0, 305, 297, 1053, 223, 1037, 63, 1881, 507, 333, 1911, 1765, 0, 0, 0, 0, 0, 151, 82, 1931, 41, 1895, 24, 22, 35, 69, 962, 588, 21, 825, 52, 0, 0, 0, 1860, 56, 1582, 0, 0, 1562, 1885, 0, 0, 137, 1094, 134, 29, 22, 522, 50, 68, 16, 40, 35, 135, 1413, 772, 50, 1015, 0, 65, 1900, 1302, 1977, 0, 29, 36, 138, 0, 67, 26, 25, 33, 37, 50, 270, 12, 316, 41, 224, 105, 1925, 1021, 1213, 172, 28, 745, 187, 147, 136, 34, 41, 636, 570, 1149, 61, 1890, 18, 143, 1517, 0, 943, 0, 0, 11, 101, 1909, 800, 0, 44, 0, 44, 31, 0, 20, 11, 13, 1924, 113, 0, 139, 12, 37, 1866, 47, 1468, 729, 24, 0, 0, 0, 14, 0, 29, 0, 70, 46, 14, 48, 1855, 113, 0, 0, 0, 54, 1923, 630, 31, 69, 0, 11, 0, 30, 50, 45, 28, 114, 193, 21, 91, 31, 1469, 1924, 87, 77, 11, 47, 21, 47, 70, 1838, 19, 531, 11, 941, 113, 26, 0, 56, 73, 32, 128, 623, 12, 52, 11, 1674, 81]                      

Frequency_list_75 = [23, 84, 290, 43, 13, 12, 22, 11, 7, 13, 107, 205, 0, 0, 106, 14, 0, 18, 116, 0, 0, 14, 286, 32, 25, 18, 36, 17, 229, 0, 0, 75, 14, 289, 284, 256, 18, 0, 0, 0, 73, 0, 56, 15, 275, 9, 20, 41, 0, 238, 0, 0, 51, 26, 12, 73, 142, 277, 292, 255, 256, 17, 0, 7, 67, 81, 17, 154, 0, 19, 0, 17, 11, 8, 122, 129, 273, 0, 38, 232, 41, 0, 77, 22, 0, 60, 78, 293, 84, 291, 9, 27, 0, 283, 95, 63, 31, 10, 279, 8, 7, 12, 17, 62, 0, 0, 16, 16, 33, 262, 19, 62, 0, 62, 67, 33, 0, 272, 0, 19, 8, 16, 0, 16, 243, 0, 289, 25, 29, 0, 0, 230, 288, 8, 13, 0, 105, 60, 291, 0, 15, 0, 280, 0, 23, 262, 0, 31, 9, 250, 0, 33, 19, 218, 0, 30, 10, 16, 0, 138, 0, 28, 8, 0, 22, 20, 0, 7, 128, 11, 64, 0, 278, 14, 45, 27, 286, 45, 0, 7, 260, 0, 156, 21, 48, 31, 12, 11, 228, 30, 16, 97, 10, 196, 49, 0, 9, 28, 21, 13, 12, 0, 38, 196, 10, 25, 291, 45, 0, 0, 7, 15, 9, 0, 0, 15, 207, 33, 13, 23, 13, 0, 0, 8, 290, 40, 8, 12, 34, 288, 0, 291, 0, 0, 66, 0, 0, 0, 29, 0, 25, 7, 9, 0, 0, 18, 0, 11, 14, 0, 0, 159, 131, 47, 56, 23, 0, 0, 45, 14, 152, 0, 17, 12, 0, 0, 31, 14, 0, 0, 282, 164, 14, 11, 0, 54, 143, 18, 8, 12, 0, 0, 12, 61, 121, 12, 0, 17, 7, 18, 0, 0, 15, 0, 0, 285, 139, 180, 134, 0, 0, 0, 47, 0, 66, 32, 0, 8, 40, 0, 16, 31, 0, 7, 19, 0, 0, 8, 57, 7, 0, 20, 0, 62, 7, 0, 15, 15, 14, 182, 0, 7, 0, 24, 25, 13, 19, 63, 7, 13, 8, 61, 8, 244, 12, 63, 41, 0, 0, 287, 209, 0, 0, 0, 0, 7, 0, 61, 18, 10, 192, 0, 24, 0, 0, 0, 59, 0, 34, 27, 9, 40, 75, 0, 67, 0, 33, 293, 118, 37, 0, 0, 7, 13, 266, 24, 179, 0, 0, 293, 10, 240, 18, 34, 36, 10, 0, 0, 13, 0, 52, 7, 10, 63, 0, 23, 0, 16, 113, 0, 288, 15, 0, 0, 52, 0, 17, 16, 18, 0, 236, 286, 7, 28, 9, 0, 0, 0, 118, 289, 0, 0, 14, 7, 0, 20, 54, 14, 0, 39, 37, 41, 13, 110, 220, 0, 35, 8, 0, 15, 289, 82, 9, 8, 26, 0, 16, 0, 81, 10, 274, 11, 12, 24, 10, 13, 0, 12, 284, 7, 7, 7, 14, 273, 17, 9, 39, 0, 0, 0, 0, 0, 9, 17, 13, 0, 23, 0, 11, 11, 0, 0, 22, 19, 9, 286, 9, 291, 19, 287, 32, 245, 0, 7, 10, 0, 23, 0, 0, 60, 20, 15, 0, 38, 32, 0, 84, 0, 13, 9, 53, 31, 14, 37, 8, 7, 0, 56, 19, 12, 7, 0, 7, 37, 0, 54, 30, 0, 25, 287, 0, 0, 0, 292, 0, 21, 158, 145, 282, 31, 0, 26, 24, 21, 18, 289, 0, 30, 258, 0, 0, 19, 113, 10, 52, 70, 0, 0, 289, 72, 0, 0, 11, 0, 0, 8, 0, 44, 24, 0, 11, 0, 0, 26, 0, 38, 11, 11, 285, 8, 94, 291, 16, 0, 293, 15, 0, 7, 0, 8, 16, 0, 0, 32, 53, 0, 7, 11, 104, 0, 264, 17, 27, 164, 285, 0, 250, 0, 0, 0, 126, 29, 8, 40, 0, 277, 121, 206, 19, 93, 282, 0, 58, 0, 0, 26, 11, 0, 80, 0, 137, 288, 51, 59, 159, 0, 36, 0, 17, 8, 0, 8, 200, 8, 178, 13, 0, 79, 79, 8, 33, 0, 0, 0, 0, 13, 30, 65, 39, 39, 0, 0, 16, 0, 168, 82, 14, 0, 57, 25, 17, 13, 15, 8, 68, 0, 178, 0, 34, 0, 0, 99, 0, 288, 22, 30, 18, 230, 26, 93, 59, 0, 268, 65, 222, 71, 23, 153, 91, 0, 8, 0, 276, 0, 290, 293, 7, 8, 98, 7, 9, 0, 25, 51, 26, 33, 0, 34, 0, 0, 8, 12, 0, 129, 154, 32, 18, 37, 141, 25, 19, 11, 0, 23, 36, 32, 16, 183, 33, 10, 59, 0, 25, 0, 0, 78, 101, 0, 0, 11, 7, 25, 9, 0, 9, 104, 18, 30, 0, 7, 59, 0, 26, 25, 13, 34, 8, 0, 0, 12, 80, 44, 0, 0, 0, 18, 0, 0, 36, 24, 7, 0, 291, 27, 8, 0, 10, 35, 58, 107, 23, 29, 0, 290, 0, 51, 9, 0, 0, 0, 231, 0, 7, 63, 0, 286, 111, 292, 19, 0, 13, 0, 0, 0, 9, 8, 287, 131, 0, 12, 0, 57, 15, 8, 152, 103, 230, 74, 21, 13, 27, 7, 30, 10, 52, 11, 20, 133, 0, 0, 0, 0, 0, 27, 0, 0, 8, 0, 0, 88, 23, 0, 15, 0, 50, 32, 19, 13, 0, 20, 15, 25, 0, 13, 11, 193, 22, 9, 9, 131, 191, 13, 0, 8, 256, 0, 0, 14, 21, 0, 0, 0, 18, 0, 13, 80, 12, 238, 110, 81, 15, 0, 55, 34, 0, 15, 7, 0, 7, 83, 181, 148, 0, 0, 63, 58, 0, 0, 45, 0, 144, 23, 138, 42, 0, 19, 38, 26, 7, 7, 0, 16, 7, 7, 30, 8, 0, 0, 0, 9, 0, 0, 172, 0, 0, 10, 293, 290, 41, 17, 296, 0, 40, 26, 87, 0, 87, 0, 280, 13, 258, 296, 8, 291, 276, 90, 296, 200, 0, 21, 9, 0, 22, 0, 0, 195, 14, 8, 105, 95, 293, 288, 0, 13, 0, 43, 0, 12, 11, 0, 0, 0, 74, 0, 296, 18, 7, 0, 28, 195, 7, 22, 0, 8, 0, 23, 7, 130, 20, 0, 133, 0, 15, 20, 0, 0, 73, 72, 185, 58, 183, 23, 286, 107, 78, 290, 273, 0, 0, 0, 0, 0, 44, 28, 292, 17, 288, 11, 11, 15, 24, 173, 120, 10, 154, 20, 0, 0, 0, 284, 21, 251, 0, 0, 249, 287, 0, 0, 41, 191, 40, 13, 11, 110, 19, 24, 8, 16, 15, 40, 231, 147, 19, 180, 0, 23, 288, 217, 297, 0, 13, 15, 41, 0, 24, 12, 12, 14, 16, 19, 67, 7, 75, 17, 58, 33, 291, 181, 206, 48, 13, 143, 51, 43, 40, 15, 17, 127, 117, 198, 22, 287, 9, 42, 244, 0, 171, 0, 0, 7, 32, 289, 151, 0, 18, 0, 18, 14, 0, 10, 7, 7, 291, 35, 0, 41, 7, 16, 284, 18, 238, 141, 11, 0, 0, 0, 8, 0, 13, 0, 25, 18, 8, 19, 283, 35, 0, 0, 0, 20, 291, 126, 14, 24, 0, 7, 0, 13, 19, 18, 13, 35, 52, 10, 30, 14, 238, 291, 29, 26, 7, 18, 10, 18, 25, 281, 10, 111, 7, 170, 35, 12, 0, 21, 25, 14, 39, 125, 7, 20, 7, 262, 27]
Frequency_list_70 = [19, 63, 199, 34, 11, 10, 18, 9, 6, 11, 79, 144, 0, 0, 78, 12, 0, 15, 84, 0, 0, 12, 197, 25, 20, 15, 29, 14, 159, 0, 0, 56, 12, 198, 195, 177, 15, 0, 0, 0, 55, 0, 43, 13, 190, 8, 16, 32, 0, 165, 0, 0, 39, 21, 10, 55, 102, 190, 200, 176, 177, 14, 0, 6, 51, 61, 14, 110, 0, 16, 0, 14, 10, 7, 89, 93, 188, 0, 30, 161, 32, 0, 57, 18, 0, 46, 59, 201, 63, 199, 8, 22, 0, 194, 70, 48, 25, 9, 192, 7, 6, 10, 14, 47, 0, 0, 14, 14, 26, 181, 16, 47, 0, 47, 51, 26, 0, 187, 0, 16, 7, 14, 0, 13, 168, 0, 198, 20, 23, 0, 0, 160, 198, 7, 11, 0, 77, 46, 199, 0, 13, 0, 193, 0, 19, 181, 0, 25, 8, 173, 0, 26, 16, 153, 0, 24, 8, 13, 0, 99, 0, 22, 7, 0, 18, 16, 0, 7, 93, 10, 49, 0, 191, 12, 35, 22, 197, 35, 0, 6, 180, 0, 111, 17, 38, 25, 11, 9, 159, 24, 14, 72, 9, 138, 38, 0, 8, 23, 17, 11, 10, 0, 30, 138, 8, 20, 200, 35, 0, 0, 7, 13, 8, 0, 0, 13, 145, 26, 11, 19, 11, 0, 0, 7, 199, 31, 7, 11, 27, 197, 0, 200, 0, 0, 50, 0, 0, 0, 23, 0, 20, 7, 8, 0, 0, 15, 0, 9, 12, 0, 0, 114, 95, 37, 43, 19, 0, 0, 35, 12, 109, 0, 14, 11, 0, 0, 25, 12, 0, 0, 194, 117, 12, 9, 0, 42, 103, 15, 7, 10, 0, 0, 10, 47, 88, 10, 0, 14, 7, 15, 0, 0, 12, 0, 0, 196, 100, 127, 97, 0, 0, 0, 36, 0, 50, 26, 0, 7, 31, 0, 13, 25, 0, 6, 16, 0, 0, 7, 44, 6, 0, 17, 0, 48, 6, 0, 13, 13, 12, 129, 0, 6, 0, 19, 20, 11, 16, 48, 7, 11, 7, 46, 7, 169, 10, 48, 32, 0, 0, 197, 147, 0, 0, 0, 0, 6, 0, 46, 15, 8, 135, 0, 20, 0, 0, 0, 45, 0, 27, 22, 8, 31, 56, 0, 51, 0, 26, 201, 86, 29, 0, 0, 7, 11, 184, 19, 127, 0, 0, 201, 8, 167, 15, 27, 28, 8, 0, 0, 11, 0, 40, 6, 9, 48, 0, 19, 0, 14, 82, 0, 198, 13, 0, 0, 40, 0, 14, 13, 15, 0, 164, 196, 6, 22, 8, 0, 0, 0, 86, 198, 0, 0, 12, 6, 0, 17, 42, 12, 0, 31, 29, 32, 11, 81, 154, 0, 28, 7, 0, 13, 198, 61, 8, 7, 21, 0, 13, 0, 60, 8, 189, 9, 10, 19, 8, 11, 0, 11, 195, 6, 7, 7, 12, 188, 14, 8, 30, 0, 0, 0, 0, 0, 8, 14, 11, 0, 19, 0, 9, 10, 0, 0, 18, 16, 8, 196, 8, 199, 16, 197, 25, 170, 0, 6, 8, 0, 19, 0, 0, 46, 17, 13, 0, 30, 26, 0, 62, 0, 11, 8, 41, 25, 12, 29, 7, 6, 0, 43, 16, 10, 7, 0, 6, 30, 0, 41, 24, 0, 20, 197, 0, 0, 0, 200, 0, 17, 113, 104, 194, 25, 0, 21, 19, 17, 15, 198, 0, 24, 178, 0, 0, 16, 83, 9, 40, 53, 0, 0, 198, 54, 0, 0, 10, 0, 0, 7, 0, 34, 19, 0, 10, 0, 0, 21, 0, 30, 10, 9, 196, 7, 69, 200, 13, 0, 201, 13, 0, 7, 0, 7, 13, 0, 0, 26, 41, 0, 6, 10, 77, 0, 182, 14, 22, 117, 195, 0, 173, 0, 0, 0, 92, 24, 7, 31, 0, 191, 88, 144, 16, 69, 194, 0, 44, 0, 0, 21, 9, 0, 60, 0, 99, 198, 40, 45, 114, 0, 28, 0, 14, 7, 0, 7, 141, 7, 126, 11, 0, 59, 59, 7, 26, 0, 0, 0, 0, 11, 24, 50, 31, 30, 0, 0, 13, 0, 120, 61, 12, 0, 43, 20, 14, 11, 12, 7, 51, 0, 126, 0, 27, 0, 0, 73, 0, 198, 18, 24, 15, 160, 21, 69, 45, 0, 185, 50, 155, 54, 18, 110, 68, 0, 7, 0, 190, 0, 199, 201, 7, 7, 72, 6, 8, 0, 20, 40, 21, 26, 0, 27, 0, 0, 7, 10, 0, 93, 110, 26, 15, 29, 102, 21, 16, 9, 0, 18, 29, 25, 14, 130, 26, 9, 45, 0, 20, 0, 0, 58, 74, 0, 0, 10, 6, 20, 8, 0, 8, 77, 15, 24, 0, 6, 45, 0, 21, 20, 11, 27, 7, 0, 0, 10, 60, 34, 0, 0, 0, 15, 0, 0, 28, 20, 7, 0, 200, 22, 7, 0, 9, 28, 44, 78, 18, 23, 0, 199, 0, 39, 8, 0, 0, 0, 161, 0, 6, 48, 0, 196, 81, 200, 16, 0, 11, 0, 0, 0, 8, 7, 197, 95, 0, 10, 0, 44, 13, 7, 109, 76, 160, 56, 17, 11, 22, 6, 24, 9, 40, 9, 16, 96, 0, 0, 0, 0, 0, 22, 0, 0, 7, 0, 0, 65, 19, 0, 13, 0, 38, 25, 16, 11, 0, 16, 13, 20, 0, 11, 10, 136, 18, 8, 8, 95, 135, 11, 0, 7, 177, 0, 0, 12, 17, 0, 0, 0, 15, 0, 11, 60, 10, 165, 80, 61, 13, 0, 42, 27, 0, 12, 6, 0, 7, 62, 128, 106, 0, 0, 48, 44, 0, 0, 35, 0, 104, 19, 99, 33, 0, 16, 30, 21, 6, 6, 0, 13, 6, 7, 24, 7, 0, 0, 0, 8, 0, 0, 122, 0, 0, 8, 201, 199, 32, 14, 203, 0, 31, 21, 65, 0, 65, 0, 192, 11, 178, 203, 7, 200, 190, 67, 203, 141, 0, 17, 8, 0, 18, 0, 0, 137, 12, 7, 77, 70, 201, 198, 0, 11, 0, 33, 0, 10, 9, 0, 0, 0, 56, 0, 203, 15, 6, 0, 23, 137, 6, 18, 0, 7, 0, 19, 7, 94, 16, 0, 96, 0, 13, 17, 0, 0, 55, 54, 131, 45, 130, 19, 196, 79, 59, 199, 188, 0, 0, 0, 0, 0, 34, 22, 200, 14, 197, 10, 9, 13, 20, 123, 87, 9, 111, 16, 0, 0, 0, 195, 17, 174, 0, 0, 173, 197, 0, 0, 32, 135, 31, 11, 9, 80, 16, 20, 7, 14, 13, 31, 161, 106, 16, 128, 0, 19, 198, 152, 203, 0, 11, 13, 32, 0, 19, 10, 10, 12, 13, 16, 51, 6, 57, 14, 45, 26, 200, 128, 145, 37, 11, 103, 39, 33, 32, 12, 14, 92, 85, 139, 18, 197, 8, 33, 169, 0, 121, 0, 0, 6, 26, 198, 108, 0, 15, 0, 15, 12, 0, 9, 6, 7, 200, 28, 0, 32, 6, 13, 195, 15, 165, 101, 10, 0, 0, 0, 7, 0, 11, 0, 20, 15, 7, 16, 195, 28, 0, 0, 0, 17, 199, 92, 12, 20, 0, 6, 0, 11, 16, 15, 11, 28, 40, 9, 24, 12, 165, 200, 23, 21, 6, 15, 9, 15, 20, 193, 8, 81, 6, 121, 28, 10, 0, 17, 21, 12, 30, 91, 6, 16, 6, 181, 22]
Frequency_list_65 = [15, 47, 136, 26, 9, 9, 15, 8, 6, 9, 58, 101, 0, 0, 57, 10, 0, 12, 62, 0, 0, 10, 135, 20, 16, 13, 23, 12, 111, 0, 0, 42, 10, 136, 134, 123, 13, 0, 0, 0, 42, 0, 33, 11, 130, 7, 13, 25, 0, 115, 0, 0, 30, 17, 9, 42, 74, 131, 137, 122, 123, 12, 0, 5, 39, 45, 12, 79, 0, 13, 0, 12, 8, 7, 65, 68, 129, 0, 24, 112, 25, 0, 43, 15, 0, 35, 44, 138, 47, 137, 7, 18, 0, 134, 52, 36, 20, 8, 132, 7, 6, 9, 12, 36, 0, 0, 11, 11, 21, 125, 13, 36, 0, 36, 39, 21, 0, 129, 0, 13, 6, 11, 0, 11, 117, 0, 136, 16, 19, 0, 0, 112, 136, 6, 9, 0, 57, 35, 137, 0, 11, 0, 132, 0, 15, 125, 0, 20, 7, 120, 0, 21, 13, 107, 0, 19, 7, 11, 0, 72, 0, 18, 6, 0, 15, 13, 0, 6, 67, 8, 37, 0, 131, 10, 27, 18, 135, 27, 0, 6, 124, 0, 80, 14, 29, 20, 9, 8, 111, 19, 11, 53, 8, 97, 29, 0, 7, 18, 14, 10, 9, 0, 24, 97, 7, 17, 137, 27, 0, 0, 6, 11, 7, 0, 0, 11, 102, 21, 9, 15, 9, 0, 0, 6, 137, 25, 7, 9, 22, 136, 0, 137, 0, 0, 38, 0, 0, 0, 19, 0, 16, 6, 7, 0, 0, 12, 0, 8, 10, 0, 0, 81, 69, 28, 33, 16, 0, 0, 27, 10, 78, 0, 12, 9, 0, 0, 20, 10, 0, 0, 133, 84, 10, 8, 0, 32, 74, 12, 6, 9, 0, 0, 9, 36, 64, 9, 0, 12, 6, 13, 0, 0, 10, 0, 0, 134, 72, 90, 70, 0, 0, 0, 28, 0, 38, 21, 0, 6, 25, 0, 11, 20, 0, 6, 13, 0, 0, 7, 33, 6, 0, 14, 0, 36, 6, 0, 11, 11, 10, 91, 0, 6, 0, 16, 16, 10, 13, 37, 6, 9, 6, 35, 6, 118, 9, 37, 25, 0, 0, 135, 103, 0, 0, 0, 0, 5, 0, 35, 12, 7, 95, 0, 16, 0, 0, 0, 34, 0, 21, 18, 7, 25, 42, 0, 39, 0, 21, 138, 63, 23, 0, 0, 6, 9, 127, 16, 90, 0, 0, 138, 7, 116, 13, 21, 22, 7, 0, 0, 9, 0, 31, 6, 8, 36, 0, 15, 0, 11, 60, 0, 136, 11, 0, 0, 31, 0, 12, 11, 13, 0, 114, 135, 5, 18, 7, 0, 0, 0, 63, 136, 0, 0, 10, 5, 0, 14, 32, 10, 0, 24, 23, 25, 9, 59, 107, 0, 22, 7, 0, 11, 136, 46, 7, 6, 17, 0, 11, 0, 45, 7, 130, 8, 9, 16, 7, 9, 0, 9, 134, 5, 6, 6, 10, 130, 12, 7, 24, 0, 0, 0, 0, 0, 7, 12, 9, 0, 16, 0, 8, 8, 0, 0, 15, 13, 7, 135, 7, 137, 13, 135, 20, 118, 0, 5, 7, 0, 15, 0, 0, 35, 14, 11, 0, 23, 21, 0, 47, 0, 10, 7, 32, 20, 10, 23, 7, 6, 0, 33, 13, 9, 6, 0, 5, 23, 0, 32, 19, 0, 17, 135, 0, 0, 0, 137, 0, 14, 81, 75, 133, 20, 0, 17, 16, 14, 12, 136, 0, 19, 123, 0, 0, 13, 61, 8, 31, 40, 0, 0, 136, 41, 0, 0, 8, 0, 0, 7, 0, 27, 16, 0, 8, 0, 0, 17, 0, 24, 8, 8, 134, 7, 51, 137, 11, 0, 138, 11, 0, 6, 0, 6, 11, 0, 0, 20, 31, 0, 6, 8, 56, 0, 126, 12, 18, 83, 134, 0, 120, 0, 0, 0, 66, 19, 6, 25, 0, 131, 64, 101, 13, 51, 133, 0, 34, 0, 0, 17, 8, 0, 45, 0, 72, 136, 31, 34, 81, 0, 22, 0, 12, 6, 0, 6, 99, 6, 89, 9, 0, 45, 44, 6, 21, 0, 0, 0, 0, 9, 19, 38, 24, 24, 0, 0, 11, 0, 85, 46, 10, 0, 33, 16, 12, 9, 10, 7, 39, 0, 90, 0, 21, 0, 0, 54, 0, 136, 15, 19, 13, 112, 17, 51, 34, 0, 127, 38, 108, 41, 15, 78, 50, 0, 7, 0, 131, 0, 136, 138, 6, 7, 53, 6, 7, 0, 16, 31, 17, 21, 0, 22, 0, 0, 6, 9, 0, 68, 79, 20, 13, 23, 73, 17, 13, 8, 0, 15, 23, 20, 11, 92, 21, 8, 35, 0, 17, 0, 0, 44, 55, 0, 0, 8, 5, 17, 7, 0, 7, 56, 13, 20, 0, 6, 35, 0, 17, 16, 10, 22, 6, 0, 0, 9, 45, 27, 0, 0, 0, 13, 0, 0, 22, 16, 6, 0, 137, 18, 6, 0, 8, 22, 34, 57, 15, 19, 0, 136, 0, 30, 7, 0, 0, 0, 112, 0, 6, 37, 0, 135, 59, 137, 13, 0, 9, 0, 0, 0, 7, 6, 135, 69, 0, 9, 0, 34, 11, 7, 78, 56, 112, 42, 14, 9, 18, 5, 19, 8, 31, 8, 14, 69, 0, 0, 0, 0, 0, 18, 0, 0, 6, 0, 0, 49, 16, 0, 11, 0, 30, 20, 13, 9, 0, 14, 11, 16, 0, 9, 8, 96, 15, 7, 7, 69, 95, 9, 0, 6, 123, 0, 0, 10, 14, 0, 0, 0, 13, 0, 9, 45, 9, 115, 59, 45, 11, 0, 33, 21, 0, 10, 6, 0, 6, 46, 91, 76, 0, 0, 36, 34, 0, 0, 27, 0, 75, 15, 72, 26, 0, 13, 24, 17, 5, 5, 0, 11, 5, 6, 19, 6, 0, 0, 0, 7, 0, 0, 87, 0, 0, 7, 138, 137, 25, 12, 139, 0, 25, 17, 48, 0, 48, 0, 132, 9, 123, 139, 7, 137, 131, 50, 139, 99, 0, 14, 7, 0, 15, 0, 0, 97, 10, 7, 57, 52, 138, 136, 0, 9, 0, 26, 0, 9, 8, 0, 0, 0, 42, 0, 139, 12, 6, 0, 18, 97, 5, 15, 0, 7, 0, 16, 6, 68, 13, 0, 69, 0, 11, 14, 0, 0, 42, 41, 93, 34, 92, 15, 135, 58, 44, 136, 129, 0, 0, 0, 0, 0, 27, 18, 137, 12, 136, 8, 8, 11, 16, 87, 64, 8, 79, 14, 0, 0, 0, 134, 14, 121, 0, 0, 120, 135, 0, 0, 25, 95, 25, 9, 8, 59, 13, 16, 7, 11, 11, 25, 112, 76, 13, 90, 0, 16, 136, 106, 139, 0, 9, 11, 25, 0, 16, 9, 9, 10, 11, 13, 39, 6, 43, 12, 34, 21, 137, 91, 102, 29, 9, 74, 30, 26, 25, 10, 12, 67, 62, 98, 15, 135, 7, 26, 117, 0, 86, 0, 0, 5, 21, 136, 78, 0, 12, 0, 12, 10, 0, 8, 5, 6, 137, 22, 0, 25, 6, 11, 134, 13, 115, 73, 8, 0, 0, 0, 6, 0, 9, 0, 16, 13, 6, 13, 134, 22, 0, 0, 0, 14, 137, 67, 10, 16, 0, 5, 0, 10, 13, 12, 9, 22, 31, 8, 19, 10, 115, 137, 19, 17, 5, 13, 8, 13, 16, 133, 7, 60, 5, 86, 22, 9, 0, 14, 17, 10, 24, 66, 6, 14, 5, 125, 18]

Frequency_list_all_1 = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 0, 0, 0, 1, 0, 1, 1, 1, 0, 0, 1, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 0, 1, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 1, 0, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 0, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 0, 0, 1, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0, 1, 1, 0, 0, 0, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
Frequency_list_total_1 = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]

novel_class = torch.tensor([
          12,   13,   16,   19,   20,   29,   30,   37,   38,   39,   41,   48,
          50,   51,   62,   68,   70,   77,   81,   84,   92,  104,  105,  112,
         116,  118,  122,  125,  129,  130,  135,  139,  141,  143,  146,  150,
         154,  158,  160,  163,  166,  171,  178,  181,  195,  201,  208,  209,
         213,  214,  221,  222,  230,  232,  233,  235,  236,  237,  239,  243,
         244,  246,  249,  250,  256,  257,  261,  264,  265,  268,  269,  274,
         280,  281,  286,  290,  291,  293,  294,  299,  300,  301,  303,  306,
         309,  312,  315,  316,  320,  322,  325,  330,  332,  347,  348,  351,
         352,  353,  354,  356,  361,  363,  364,  365,  367,  373,  375,  380,
         381,  387,  388,  396,  397,  399,  404,  406,  409,  412,  413,  415,
         419,  425,  426,  427,  430,  431,  434,  438,  445,  448,  455,  457,
         466,  477,  478,  479,  480,  481,  485,  487,  490,  491,  502,  505,
         507,  508,  512,  515,  517,  526,  531,  534,  537,  540,  541,  542,
         544,  550,  556,  559,  560,  566,  567,  570,  571,  573,  574,  576,
         579,  581,  582,  584,  593,  596,  598,  601,  602,  605,  609,  615,
         617,  618,  619,  624,  631,  633,  634,  637,  639,  645,  647,  650,
         656,  661,  662,  663,  664,  670,  671,  673,  677,  685,  687,  689,
         690,  692,  701,  709,  711,  713,  721,  726,  728,  729,  732,  742,
         751,  753,  754,  757,  758,  763,  768,  771,  777,  778,  782,  783,
         784,  786,  787,  791,  795,  802,  804,  807,  808,  809,  811,  814,
         819,  821,  822,  823,  828,  830,  848,  849,  850,  851,  852,  854,
         855,  857,  858,  861,  863,  868,  872,  882,  885,  886,  889,  890,
         891,  893,  901,  904,  907,  912,  913,  916,  917,  919,  924,  930,
         936,  937,  938,  940,  941,  943,  944,  951,  955,  957,  968,  971,
         973,  974,  982,  984,  986,  989,  990,  991,  993,  997, 1002, 1004,
        1009, 1011, 1014, 1015, 1027, 1028, 1029, 1030, 1031, 1046, 1047, 1048,
        1052, 1053, 1056, 1057, 1074, 1079, 1083, 1115, 1117, 1118, 1123, 1125,
        1128, 1134, 1143, 1144, 1145, 1147, 1149, 1156, 1157, 1158, 1164, 1166,
        1192])

# +1
novel_list = torch.tensor([13, 14, 17, 20, 21, 30, 31, 38, 39, 40, 42, 49, 51, 52, 63, 69, 71, 78, 82, 85, 93, 105, 106, 113, 117, 119, 123, 126, 130, 131, 136, 140, 142, 144, 147, 151, 155, 159, 161, 164, 167, 172, 179, 182, 196, 202, 209, 210, 214, 215, 222, 223, 231, 233, 234, 236, 237, 238, 240, 244, 245, 247, 250, 251, 257, 258, 262, 265, 266, 269, 270, 275, 281, 282, 287, 291, 292, 294, 295, 300, 301, 302, 304, 307, 310, 313, 316, 317, 321, 323, 326, 331, 333, 348, 349, 352, 353, 354, 355, 357, 362, 364, 365, 366, 368, 374, 376, 381, 382, 388, 389, 397, 398, 400, 405, 407, 410, 413, 414, 416, 420, 426, 427, 428, 431, 432, 435, 439, 446, 449, 456, 458, 467, 478, 479, 480, 481, 482, 486, 488, 491, 492, 503, 506, 508, 509, 513, 516, 518, 527, 532, 535, 538, 541, 542, 543, 545, 551, 557, 560, 561, 567, 568, 571, 572, 574, 575, 577, 580, 582, 583, 585, 594, 597, 599, 602, 603, 606, 610, 616, 618, 619, 620, 625, 632, 634, 635, 638, 640, 646, 648, 651, 657, 662, 663, 664, 665, 671, 672, 674, 678, 686, 688, 690, 691, 693, 702, 710, 712, 714, 722, 727, 729, 730, 733, 743, 752, 754, 755, 758, 759, 764, 769, 772, 778, 779, 783, 784, 785, 787, 788, 792, 796, 803, 805, 808, 809, 810, 812, 815, 820, 822, 823, 824, 829, 831, 849, 850, 851, 852, 853, 855, 856, 858, 859, 862, 864, 869, 873, 883, 886, 887, 890, 891, 892, 894, 902, 905, 908, 913, 914, 917, 918, 920, 925, 931, 937, 938, 939, 941, 942, 944, 945, 952, 956, 958, 969, 972, 974, 975, 983, 985, 987, 990, 991, 992, 994, 998, 1003, 1005, 1010, 1012, 1015, 1016, 1028, 1029, 1030, 1031, 1032, 1047, 1048, 1049, 1053, 1054, 1057, 1058, 1075, 1080, 1084, 1116, 1118, 1119, 1124, 1126, 1129, 1135, 1144, 1145, 1146, 1148, 1150, 1157, 1158, 1159, 1165, 1167, 1193])


CLASSES = (
        'aerosol_can', 'air_conditioner', 'airplane', 'alarm_clock', 'alcohol',
        'alligator', 'almond', 'ambulance', 'amplifier', 'anklet', 'antenna',
        'apple', 'applesauce', 'apricot', 'apron', 'aquarium',
        'arctic_(type_of_shoe)', 'armband', 'armchair', 'armoire', 'armor',
        'artichoke', 'trash_can', 'ashtray', 'asparagus', 'atomizer',
        'avocado', 'award', 'awning', 'ax', 'baboon', 'baby_buggy',
        'basketball_backboard', 'backpack', 'handbag', 'suitcase', 'bagel',
        'bagpipe', 'baguet', 'bait', 'ball', 'ballet_skirt', 'balloon',
        'bamboo', 'banana', 'Band_Aid', 'bandage', 'bandanna', 'banjo',
        'banner', 'barbell', 'barge', 'barrel', 'barrette', 'barrow',
        'baseball_base', 'baseball', 'baseball_bat', 'baseball_cap',
        'baseball_glove', 'basket', 'basketball', 'bass_horn', 'bat_(animal)',
        'bath_mat', 'bath_towel', 'bathrobe', 'bathtub', 'batter_(food)',
        'battery', 'beachball', 'bead', 'bean_curd', 'beanbag', 'beanie',
        'bear', 'bed', 'bedpan', 'bedspread', 'cow', 'beef_(food)', 'beeper',
        'beer_bottle', 'beer_can', 'beetle', 'bell', 'bell_pepper', 'belt',
        'belt_buckle', 'bench', 'beret', 'bib', 'Bible', 'bicycle', 'visor',
        'billboard', 'binder', 'binoculars', 'bird', 'birdfeeder', 'birdbath',
        'birdcage', 'birdhouse', 'birthday_cake', 'birthday_card',
        'pirate_flag', 'black_sheep', 'blackberry', 'blackboard', 'blanket',
        'blazer', 'blender', 'blimp', 'blinker', 'blouse', 'blueberry',
        'gameboard', 'boat', 'bob', 'bobbin', 'bobby_pin', 'boiled_egg',
        'bolo_tie', 'deadbolt', 'bolt', 'bonnet', 'book', 'bookcase',
        'booklet', 'bookmark', 'boom_microphone', 'boot', 'bottle',
        'bottle_opener', 'bouquet', 'bow_(weapon)', 'bow_(decorative_ribbons)',
        'bow-tie', 'bowl', 'pipe_bowl', 'bowler_hat', 'bowling_ball', 'box',
        'boxing_glove', 'suspenders', 'bracelet', 'brass_plaque', 'brassiere',
        'bread-bin', 'bread', 'breechcloth', 'bridal_gown', 'briefcase',
        'broccoli', 'broach', 'broom', 'brownie', 'brussels_sprouts',
        'bubble_gum', 'bucket', 'horse_buggy', 'bull', 'bulldog', 'bulldozer',
        'bullet_train', 'bulletin_board', 'bulletproof_vest', 'bullhorn',
        'bun', 'bunk_bed', 'buoy', 'burrito', 'bus_(vehicle)', 'business_card',
        'butter', 'butterfly', 'button', 'cab_(taxi)', 'cabana', 'cabin_car',
        'cabinet', 'locker', 'cake', 'calculator', 'calendar', 'calf',
        'camcorder', 'camel', 'camera', 'camera_lens', 'camper_(vehicle)',
        'can', 'can_opener', 'candle', 'candle_holder', 'candy_bar',
        'candy_cane', 'walking_cane', 'canister', 'canoe', 'cantaloup',
        'canteen', 'cap_(headwear)', 'bottle_cap', 'cape', 'cappuccino',
        'car_(automobile)', 'railcar_(part_of_a_train)', 'elevator_car',
        'car_battery', 'identity_card', 'card', 'cardigan', 'cargo_ship',
        'carnation', 'horse_carriage', 'carrot', 'tote_bag', 'cart', 'carton',
        'cash_register', 'casserole', 'cassette', 'cast', 'cat', 'cauliflower',
        'cayenne_(spice)', 'CD_player', 'celery', 'cellular_telephone',
        'chain_mail', 'chair', 'chaise_longue', 'chalice', 'chandelier',
        'chap', 'checkbook', 'checkerboard', 'cherry', 'chessboard',
        'chicken_(animal)', 'chickpea', 'chili_(vegetable)', 'chime',
        'chinaware', 'crisp_(potato_chip)', 'poker_chip', 'chocolate_bar',
        'chocolate_cake', 'chocolate_milk', 'chocolate_mousse', 'choker',
        'chopping_board', 'chopstick', 'Christmas_tree', 'slide', 'cider',
        'cigar_box', 'cigarette', 'cigarette_case', 'cistern', 'clarinet',
        'clasp', 'cleansing_agent', 'cleat_(for_securing_rope)', 'clementine',
        'clip', 'clipboard', 'clippers_(for_plants)', 'cloak', 'clock',
        'clock_tower', 'clothes_hamper', 'clothespin', 'clutch_bag', 'coaster',
        'coat', 'coat_hanger', 'coatrack', 'cock', 'cockroach',
        'cocoa_(beverage)', 'coconut', 'coffee_maker', 'coffee_table',
        'coffeepot', 'coil', 'coin', 'colander', 'coleslaw',
        'coloring_material', 'combination_lock', 'pacifier', 'comic_book',
        'compass', 'computer_keyboard', 'condiment', 'cone', 'control',
        'convertible_(automobile)', 'sofa_bed', 'cooker', 'cookie',
        'cooking_utensil', 'cooler_(for_food)', 'cork_(bottle_plug)',
        'corkboard', 'corkscrew', 'edible_corn', 'cornbread', 'cornet',
        'cornice', 'cornmeal', 'corset', 'costume', 'cougar', 'coverall',
        'cowbell', 'cowboy_hat', 'crab_(animal)', 'crabmeat', 'cracker',
        'crape', 'crate', 'crayon', 'cream_pitcher', 'crescent_roll', 'crib',
        'crock_pot', 'crossbar', 'crouton', 'crow', 'crowbar', 'crown',
        'crucifix', 'cruise_ship', 'police_cruiser', 'crumb', 'crutch',
        'cub_(animal)', 'cube', 'cucumber', 'cufflink', 'cup', 'trophy_cup',
        'cupboard', 'cupcake', 'hair_curler', 'curling_iron', 'curtain',
        'cushion', 'cylinder', 'cymbal', 'dagger', 'dalmatian', 'dartboard',
        'date_(fruit)', 'deck_chair', 'deer', 'dental_floss', 'desk',
        'detergent', 'diaper', 'diary', 'die', 'dinghy', 'dining_table', 'tux',
        'dish', 'dish_antenna', 'dishrag', 'dishtowel', 'dishwasher',
        'dishwasher_detergent', 'dispenser', 'diving_board', 'Dixie_cup',
        'dog', 'dog_collar', 'doll', 'dollar', 'dollhouse', 'dolphin',
        'domestic_ass', 'doorknob', 'doormat', 'doughnut', 'dove', 'dragonfly',
        'drawer', 'underdrawers', 'dress', 'dress_hat', 'dress_suit',
        'dresser', 'drill', 'drone', 'dropper', 'drum_(musical_instrument)',
        'drumstick', 'duck', 'duckling', 'duct_tape', 'duffel_bag', 'dumbbell',
        'dumpster', 'dustpan', 'eagle', 'earphone', 'earplug', 'earring',
        'easel', 'eclair', 'eel', 'egg', 'egg_roll', 'egg_yolk', 'eggbeater',
        'eggplant', 'electric_chair', 'refrigerator', 'elephant', 'elk',
        'envelope', 'eraser', 'escargot', 'eyepatch', 'falcon', 'fan',
        'faucet', 'fedora', 'ferret', 'Ferris_wheel', 'ferry', 'fig_(fruit)',
        'fighter_jet', 'figurine', 'file_cabinet', 'file_(tool)', 'fire_alarm',
        'fire_engine', 'fire_extinguisher', 'fire_hose', 'fireplace',
        'fireplug', 'first-aid_kit', 'fish', 'fish_(food)', 'fishbowl',
        'fishing_rod', 'flag', 'flagpole', 'flamingo', 'flannel', 'flap',
        'flash', 'flashlight', 'fleece', 'flip-flop_(sandal)',
        'flipper_(footwear)', 'flower_arrangement', 'flute_glass', 'foal',
        'folding_chair', 'food_processor', 'football_(American)',
        'football_helmet', 'footstool', 'fork', 'forklift', 'freight_car',
        'French_toast', 'freshener', 'frisbee', 'frog', 'fruit_juice',
        'frying_pan', 'fudge', 'funnel', 'futon', 'gag', 'garbage',
        'garbage_truck', 'garden_hose', 'gargle', 'gargoyle', 'garlic',
        'gasmask', 'gazelle', 'gelatin', 'gemstone', 'generator',
        'giant_panda', 'gift_wrap', 'ginger', 'giraffe', 'cincture',
        'glass_(drink_container)', 'globe', 'glove', 'goat', 'goggles',
        'goldfish', 'golf_club', 'golfcart', 'gondola_(boat)', 'goose',
        'gorilla', 'gourd', 'grape', 'grater', 'gravestone', 'gravy_boat',
        'green_bean', 'green_onion', 'griddle', 'grill', 'grits', 'grizzly',
        'grocery_bag', 'guitar', 'gull', 'gun', 'hairbrush', 'hairnet',
        'hairpin', 'halter_top', 'ham', 'hamburger', 'hammer', 'hammock',
        'hamper', 'hamster', 'hair_dryer', 'hand_glass', 'hand_towel',
        'handcart', 'handcuff', 'handkerchief', 'handle', 'handsaw',
        'hardback_book', 'harmonium', 'hat', 'hatbox', 'veil', 'headband',
        'headboard', 'headlight', 'headscarf', 'headset',
        'headstall_(for_horses)', 'heart', 'heater', 'helicopter', 'helmet',
        'heron', 'highchair', 'hinge', 'hippopotamus', 'hockey_stick', 'hog',
        'home_plate_(baseball)', 'honey', 'fume_hood', 'hook', 'hookah',
        'hornet', 'horse', 'hose', 'hot-air_balloon', 'hotplate', 'hot_sauce',
        'hourglass', 'houseboat', 'hummingbird', 'hummus', 'polar_bear',
        'icecream', 'popsicle', 'ice_maker', 'ice_pack', 'ice_skate',
        'igniter', 'inhaler', 'iPod', 'iron_(for_clothing)', 'ironing_board',
        'jacket', 'jam', 'jar', 'jean', 'jeep', 'jelly_bean', 'jersey',
        'jet_plane', 'jewel', 'jewelry', 'joystick', 'jumpsuit', 'kayak',
        'keg', 'kennel', 'kettle', 'key', 'keycard', 'kilt', 'kimono',
        'kitchen_sink', 'kitchen_table', 'kite', 'kitten', 'kiwi_fruit',
        'knee_pad', 'knife', 'knitting_needle', 'knob', 'knocker_(on_a_door)',
        'koala', 'lab_coat', 'ladder', 'ladle', 'ladybug', 'lamb_(animal)',
        'lamb-chop', 'lamp', 'lamppost', 'lampshade', 'lantern', 'lanyard',
        'laptop_computer', 'lasagna', 'latch', 'lawn_mower', 'leather',
        'legging_(clothing)', 'Lego', 'legume', 'lemon', 'lemonade', 'lettuce',
        'license_plate', 'life_buoy', 'life_jacket', 'lightbulb',
        'lightning_rod', 'lime', 'limousine', 'lion', 'lip_balm', 'liquor',
        'lizard', 'log', 'lollipop', 'speaker_(stereo_equipment)', 'loveseat',
        'machine_gun', 'magazine', 'magnet', 'mail_slot', 'mailbox_(at_home)',
        'mallard', 'mallet', 'mammoth', 'manatee', 'mandarin_orange', 'manger',
        'manhole', 'map', 'marker', 'martini', 'mascot', 'mashed_potato',
        'masher', 'mask', 'mast', 'mat_(gym_equipment)', 'matchbox',
        'mattress', 'measuring_cup', 'measuring_stick', 'meatball', 'medicine',
        'melon', 'microphone', 'microscope', 'microwave_oven', 'milestone',
        'milk', 'milk_can', 'milkshake', 'minivan', 'mint_candy', 'mirror',
        'mitten', 'mixer_(kitchen_tool)', 'money',
        'monitor_(computer_equipment) computer_monitor', 'monkey', 'motor',
        'motor_scooter', 'motor_vehicle', 'motorcycle', 'mound_(baseball)',
        'mouse_(computer_equipment)', 'mousepad', 'muffin', 'mug', 'mushroom',
        'music_stool', 'musical_instrument', 'nailfile', 'napkin',
        'neckerchief', 'necklace', 'necktie', 'needle', 'nest', 'newspaper',
        'newsstand', 'nightshirt', 'nosebag_(for_animals)',
        'noseband_(for_animals)', 'notebook', 'notepad', 'nut', 'nutcracker',
        'oar', 'octopus_(food)', 'octopus_(animal)', 'oil_lamp', 'olive_oil',
        'omelet', 'onion', 'orange_(fruit)', 'orange_juice', 'ostrich',
        'ottoman', 'oven', 'overalls_(clothing)', 'owl', 'packet', 'inkpad',
        'pad', 'paddle', 'padlock', 'paintbrush', 'painting', 'pajamas',
        'palette', 'pan_(for_cooking)', 'pan_(metal_container)', 'pancake',
        'pantyhose', 'papaya', 'paper_plate', 'paper_towel', 'paperback_book',
        'paperweight', 'parachute', 'parakeet', 'parasail_(sports)', 'parasol',
        'parchment', 'parka', 'parking_meter', 'parrot',
        'passenger_car_(part_of_a_train)', 'passenger_ship', 'passport',
        'pastry', 'patty_(food)', 'pea_(food)', 'peach', 'peanut_butter',
        'pear', 'peeler_(tool_for_fruit_and_vegetables)', 'wooden_leg',
        'pegboard', 'pelican', 'pen', 'pencil', 'pencil_box',
        'pencil_sharpener', 'pendulum', 'penguin', 'pennant', 'penny_(coin)',
        'pepper', 'pepper_mill', 'perfume', 'persimmon', 'person', 'pet',
        'pew_(church_bench)', 'phonebook', 'phonograph_record', 'piano',
        'pickle', 'pickup_truck', 'pie', 'pigeon', 'piggy_bank', 'pillow',
        'pin_(non_jewelry)', 'pineapple', 'pinecone', 'ping-pong_ball',
        'pinwheel', 'tobacco_pipe', 'pipe', 'pistol', 'pita_(bread)',
        'pitcher_(vessel_for_liquid)', 'pitchfork', 'pizza', 'place_mat',
        'plate', 'platter', 'playpen', 'pliers', 'plow_(farm_equipment)',
        'plume', 'pocket_watch', 'pocketknife', 'poker_(fire_stirring_tool)',
        'pole', 'polo_shirt', 'poncho', 'pony', 'pool_table', 'pop_(soda)',
        'postbox_(public)', 'postcard', 'poster', 'pot', 'flowerpot', 'potato',
        'potholder', 'pottery', 'pouch', 'power_shovel', 'prawn', 'pretzel',
        'printer', 'projectile_(weapon)', 'projector', 'propeller', 'prune',
        'pudding', 'puffer_(fish)', 'puffin', 'pug-dog', 'pumpkin', 'puncher',
        'puppet', 'puppy', 'quesadilla', 'quiche', 'quilt', 'rabbit',
        'race_car', 'racket', 'radar', 'radiator', 'radio_receiver', 'radish',
        'raft', 'rag_doll', 'raincoat', 'ram_(animal)', 'raspberry', 'rat',
        'razorblade', 'reamer_(juicer)', 'rearview_mirror', 'receipt',
        'recliner', 'record_player', 'reflector', 'remote_control',
        'rhinoceros', 'rib_(food)', 'rifle', 'ring', 'river_boat', 'road_map',
        'robe', 'rocking_chair', 'rodent', 'roller_skate', 'Rollerblade',
        'rolling_pin', 'root_beer', 'router_(computer_equipment)',
        'rubber_band', 'runner_(carpet)', 'plastic_bag',
        'saddle_(on_an_animal)', 'saddle_blanket', 'saddlebag', 'safety_pin',
        'sail', 'salad', 'salad_plate', 'salami', 'salmon_(fish)',
        'salmon_(food)', 'salsa', 'saltshaker', 'sandal_(type_of_shoe)',
        'sandwich', 'satchel', 'saucepan', 'saucer', 'sausage', 'sawhorse',
        'saxophone', 'scale_(measuring_instrument)', 'scarecrow', 'scarf',
        'school_bus', 'scissors', 'scoreboard', 'scraper', 'screwdriver',
        'scrubbing_brush', 'sculpture', 'seabird', 'seahorse', 'seaplane',
        'seashell', 'sewing_machine', 'shaker', 'shampoo', 'shark',
        'sharpener', 'Sharpie', 'shaver_(electric)', 'shaving_cream', 'shawl',
        'shears', 'sheep', 'shepherd_dog', 'sherbert', 'shield', 'shirt',
        'shoe', 'shopping_bag', 'shopping_cart', 'short_pants', 'shot_glass',
        'shoulder_bag', 'shovel', 'shower_head', 'shower_cap',
        'shower_curtain', 'shredder_(for_paper)', 'signboard', 'silo', 'sink',
        'skateboard', 'skewer', 'ski', 'ski_boot', 'ski_parka', 'ski_pole',
        'skirt', 'skullcap', 'sled', 'sleeping_bag', 'sling_(bandage)',
        'slipper_(footwear)', 'smoothie', 'snake', 'snowboard', 'snowman',
        'snowmobile', 'soap', 'soccer_ball', 'sock', 'sofa', 'softball',
        'solar_array', 'sombrero', 'soup', 'soup_bowl', 'soupspoon',
        'sour_cream', 'soya_milk', 'space_shuttle', 'sparkler_(fireworks)',
        'spatula', 'spear', 'spectacles', 'spice_rack', 'spider', 'crawfish',
        'sponge', 'spoon', 'sportswear', 'spotlight', 'squid_(food)',
        'squirrel', 'stagecoach', 'stapler_(stapling_machine)', 'starfish',
        'statue_(sculpture)', 'steak_(food)', 'steak_knife', 'steering_wheel',
        'stepladder', 'step_stool', 'stereo_(sound_system)', 'stew', 'stirrer',
        'stirrup', 'stool', 'stop_sign', 'brake_light', 'stove', 'strainer',
        'strap', 'straw_(for_drinking)', 'strawberry', 'street_sign',
        'streetlight', 'string_cheese', 'stylus', 'subwoofer', 'sugar_bowl',
        'sugarcane_(plant)', 'suit_(clothing)', 'sunflower', 'sunglasses',
        'sunhat', 'surfboard', 'sushi', 'mop', 'sweat_pants', 'sweatband',
        'sweater', 'sweatshirt', 'sweet_potato', 'swimsuit', 'sword',
        'syringe', 'Tabasco_sauce', 'table-tennis_table', 'table',
        'table_lamp', 'tablecloth', 'tachometer', 'taco', 'tag', 'taillight',
        'tambourine', 'army_tank', 'tank_(storage_vessel)',
        'tank_top_(clothing)', 'tape_(sticky_cloth_or_paper)', 'tape_measure',
        'tapestry', 'tarp', 'tartan', 'tassel', 'tea_bag', 'teacup',
        'teakettle', 'teapot', 'teddy_bear', 'telephone', 'telephone_booth',
        'telephone_pole', 'telephoto_lens', 'television_camera',
        'television_set', 'tennis_ball', 'tennis_racket', 'tequila',
        'thermometer', 'thermos_bottle', 'thermostat', 'thimble', 'thread',
        'thumbtack', 'tiara', 'tiger', 'tights_(clothing)', 'timer', 'tinfoil',
        'tinsel', 'tissue_paper', 'toast_(food)', 'toaster', 'toaster_oven',
        'toilet', 'toilet_tissue', 'tomato', 'tongs', 'toolbox', 'toothbrush',
        'toothpaste', 'toothpick', 'cover', 'tortilla', 'tow_truck', 'towel',
        'towel_rack', 'toy', 'tractor_(farm_equipment)', 'traffic_light',
        'dirt_bike', 'trailer_truck', 'train_(railroad_vehicle)', 'trampoline',
        'tray', 'trench_coat', 'triangle_(musical_instrument)', 'tricycle',
        'tripod', 'trousers', 'truck', 'truffle_(chocolate)', 'trunk', 'vat',
        'turban', 'turkey_(food)', 'turnip', 'turtle', 'turtleneck_(clothing)',
        'typewriter', 'umbrella', 'underwear', 'unicycle', 'urinal', 'urn',
        'vacuum_cleaner', 'vase', 'vending_machine', 'vent', 'vest',
        'videotape', 'vinegar', 'violin', 'vodka', 'volleyball', 'vulture',
        'waffle', 'waffle_iron', 'wagon', 'wagon_wheel', 'walking_stick',
        'wall_clock', 'wall_socket', 'wallet', 'walrus', 'wardrobe',
        'washbasin', 'automatic_washer', 'watch', 'water_bottle',
        'water_cooler', 'water_faucet', 'water_heater', 'water_jug',
        'water_gun', 'water_scooter', 'water_ski', 'water_tower',
        'watering_can', 'watermelon', 'weathervane', 'webcam', 'wedding_cake',
        'wedding_ring', 'wet_suit', 'wheel', 'wheelchair', 'whipped_cream',
        'whistle', 'wig', 'wind_chime', 'windmill', 'window_box_(for_plants)',
        'windshield_wiper', 'windsock', 'wine_bottle', 'wine_bucket',
        'wineglass', 'blinder_(for_horses)', 'wok', 'wolf', 'wooden_spoon',
        'wreath', 'wrench', 'wristband', 'wristlet', 'yacht', 'yogurt',
        'yoke_(animal_equipment)', 'zebra', 'zucchini')

# for eval
novel_list_ori = [
          12,   13,   16,   19,   20,   29,   30,   37,   38,   39,   41,   48,
          50,   51,   62,   68,   70,   77,   81,   84,   92,  104,  105,  112,
         116,  118,  122,  125,  129,  130,  135,  139,  141,  143,  146,  150,
         154,  158,  160,  163,  166,  171,  178,  181,  195,  201,  208,  209,
         213,  214,  221,  222,  230,  232,  233,  235,  236,  237,  239,  243,
         244,  246,  249,  250,  256,  257,  261,  264,  265,  268,  269,  274,
         280,  281,  286,  290,  291,  293,  294,  299,  300,  301,  303,  306,
         309,  312,  315,  316,  320,  322,  325,  330,  332,  347,  348,  351,
         352,  353,  354,  356,  361,  363,  364,  365,  367,  373,  375,  380,
         381,  387,  388,  396,  397,  399,  404,  406,  409,  412,  413,  415,
         419,  425,  426,  427,  430,  431,  434,  438,  445,  448,  455,  457,
         466,  477,  478,  479,  480,  481,  485,  487,  490,  491,  502,  505,
         507,  508,  512,  515,  517,  526,  531,  534,  537,  540,  541,  542,
         544,  550,  556,  559,  560,  566,  567,  570,  571,  573,  574,  576,
         579,  581,  582,  584,  593,  596,  598,  601,  602,  605,  609,  615,
         617,  618,  619,  624,  631,  633,  634,  637,  639,  645,  647,  650,
         656,  661,  662,  663,  664,  670,  671,  673,  677,  685,  687,  689,
         690,  692,  701,  709,  711,  713,  721,  726,  728,  729,  732,  742,
         751,  753,  754,  757,  758,  763,  768,  771,  777,  778,  782,  783,
         784,  786,  787,  791,  795,  802,  804,  807,  808,  809,  811,  814,
         819,  821,  822,  823,  828,  830,  848,  849,  850,  851,  852,  854,
         855,  857,  858,  861,  863,  868,  872,  882,  885,  886,  889,  890,
         891,  893,  901,  904,  907,  912,  913,  916,  917,  919,  924,  930,
         936,  937,  938,  940,  941,  943,  944,  951,  955,  957,  968,  971,
         973,  974,  982,  984,  986,  989,  990,  991,  993,  997, 1002, 1004,
        1009, 1011, 1014, 1015, 1027, 1028, 1029, 1030, 1031, 1046, 1047, 1048,
        1052, 1053, 1056, 1057, 1074, 1079, 1083, 1115, 1117, 1118, 1123, 1125,
        1128, 1134, 1143, 1144, 1145, 1147, 1149, 1156, 1157, 1158, 1164, 1166,
        1192]

# BGR
COLORS = [[145, 153, 12], [109, 191, 84], [34, 196, 132], [240, 144, 107], [55, 90, 224], [239, 100, 138], [146, 43, 128], [99, 177, 149], [218, 159, 97], [79, 118, 243],
        [231, 86, 173], [111, 212, 147], [159, 11, 171], [202, 121, 47], [214, 207, 80], [89, 187, 48], [204, 73, 123], [84, 233, 128], [134, 54, 172], [90, 203, 117],
        [155, 43, 129], [159, 105, 255], [67, 122, 177], [69, 157, 188], [203, 72, 36], [114, 218, 138], [122, 223, 114], [239, 61, 119], [222, 79, 165], [90, 222, 150],
        [79, 53, 191], [182, 166, 52], [61, 84, 157], [31, 129, 186], [179, 210, 84], [90, 230, 68], [135, 140, 232], [71, 207, 240], [174, 57, 179], [171, 81, 85],
        [161, 49, 99], [173, 49, 122], [51, 94, 168], [168, 95, 38], [58, 126, 162], [78, 214, 82], [79, 65, 165], [61, 115, 191], [94, 138, 253], [154, 37, 167],
        [251, 138, 113], [178, 36, 148], [95, 227, 87], [162, 227, 103], [207, 53, 186], [163, 58, 225], [46, 164, 210], [215, 57, 85], [200, 72, 88], [79, 46, 177],
        [217, 136, 46], [204, 116, 99], [240, 107, 139], [194, 210, 65], [184, 239, 88], [101, 199, 213], [70, 232, 211], [213, 167, 81], [205, 72, 135], [141, 249, 120],
        [160, 246, 110], [30, 158, 172], [134, 179, 7], [95, 234, 70], [101, 77, 180], [180, 171, 32], [130, 51, 167], [97, 152, 246], [228, 172, 92], [34, 204, 148],
        [175, 13, 168], [167, 41, 120], [191, 160, 21], [215, 172, 50], [254, 113, 127], [40, 105, 176], [207, 82, 210], [168, 146, 34], [89, 81, 200], [230, 146, 78],
        [49, 78, 218], [96, 157, 224], [159, 249, 111], [235, 108, 82], [71, 74, 220], [167, 59, 126], [139, 34, 133], [182, 105, 215], [114, 237, 82], [193, 55, 104],
        [232, 105, 60], [63, 193, 98], [42, 188, 77], [28, 104, 170], [153, 211, 78], [20, 143, 145], [93, 236, 167], [99, 74, 241], [188, 11, 135], [51, 225, 165],
        [58, 138, 148], [149, 236, 122], [210, 106, 135], [85, 238, 159], [202, 63, 66], [106, 237, 93], [57, 150, 146], [158, 152, 29], [79, 198, 85], [95, 202, 36],
        [95, 230, 108], [183, 196, 34], [11, 191, 102], [161, 173, 77], [124, 221, 145], [210, 129, 106], [165, 89, 224], [60, 183, 126], [52, 94, 227], [226, 171, 95],
        [180, 93, 205], [165, 163, 35], [46, 219, 184], [201, 162, 27], [227, 129, 76], [218, 87, 171], [17, 200, 99], [83, 238, 131], [162, 222, 95], [237, 172, 88],
        [153, 128, 223], [81, 251, 116], [197, 97, 60], [124, 150, 243], [230, 77, 71], [199, 85, 193], [108, 43, 189], [94, 249, 131], [191, 108, 24], [105, 141, 234],
        [217, 46, 107], [99, 174, 28], [210, 38, 131], [101, 220, 73], [31, 126, 151], [94, 190, 119], [211, 38, 114], [165, 67, 192], [70, 173, 80], [153, 95, 253],
        [183, 86, 227], [89, 186, 166], [197, 98, 206], [144, 37, 131], [35, 116, 152], [151, 132, 53], [82, 159, 241], [118, 29, 182], [146, 68, 244], [118, 27, 177],
        [138, 168, 70], [69, 61, 188], [237, 107, 120], [102, 164, 66], [161, 56, 114], [159, 230, 96], [163, 53, 218], [144, 228, 57], [216, 171, 82], [74, 174, 98],
        [208, 101, 73], [161, 219, 104], [57, 134, 196], [111, 212, 184], [19, 155, 165], [165, 149, 56], [212, 184, 65], [106, 95, 208], [218, 176, 75], [72, 175, 104],
        [78, 60, 166], [57, 220, 175], [36, 192, 102], [176, 69, 60], [55, 86, 195], [202, 64, 146], [85, 50, 217], [112, 182, 12], [178, 64, 156], [212, 185, 63],
        [173, 209, 98], [239, 88, 129], [146, 92, 191], [132, 198, 67], [82, 83, 225], [199, 86, 43], [69, 202, 217], [67, 172, 249], [91, 170, 244], [38, 190, 98],
        [214, 63, 182], [36, 106, 212], [196, 114, 13], [204, 56, 102], [237, 164, 92], [161, 204, 86], [236, 60, 138], [216, 174, 88], [61, 210, 139], [140, 43, 186],
        [85, 224, 93], [219, 56, 205], [86, 140, 233], [72, 223, 153], [209, 78, 33], [210, 46, 131], [158, 61, 244], [155, 199, 58], [38, 156, 133], [156, 42, 132],
        [208, 65, 194], [230, 83, 141], [184, 165, 45], [192, 225, 62], [213, 97, 175], [187, 215, 85], [95, 154, 237], [211, 41, 165], [36, 197, 171], [127, 195, 55],
        [115, 96, 205], [26, 173, 164], [125, 10, 179], [118, 218, 61], [236, 87, 152], [166, 226, 105], [230, 131, 55], [61, 223, 163], [107, 101, 242], [205, 160, 82],
        [164, 32, 208], [153, 238, 99], [200, 99, 44], [205, 83, 112], [58, 227, 146], [96, 103, 192], [172, 37, 92], [201, 133, 80], [31, 105, 173], [157, 51, 168],
        [141, 162, 43], [239, 188, 76], [26, 102, 182], [166, 31, 199], [23, 161, 181], [48, 175, 221], [186, 57, 228], [25, 90, 196], [235, 97, 131], [84, 226, 92],
        [78, 179, 232], [126, 136, 251], [239, 88, 190], [236, 81, 120], [151, 180, 17], [164, 43, 126], [195, 139, 48], [181, 79, 174], [93, 181, 188], [109, 167, 238],
        [64, 226, 116], [120, 193, 67], [60, 238, 120], [176, 43, 219]]

