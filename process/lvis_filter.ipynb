{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json \n", "import sys\n", "from ovtr.datasets.parsers import CocoVID\n", "ann_file='./data/lvis_image_v1.json' # lvis + coco \n", "coco = CocoVID(ann_file)\n", "with open(ann_file, 'r') as json_file:  \n", "    data = json.load(json_file)  "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def compute_occlusion(box1, box2):  \n", "    if box1[0]==box2[0] and box1[1]==box2[1] and box1[2]==box2[2] and box1[3]==box2[3]:\n", "        return True\n", "    \n", "    xi1 = max(box1[0], box2[0])  \n", "    yi1 = max(box1[1], box2[1])  \n", "    xi2 = min(box1[0] + box1[2], box2[0] + box2[2])  \n", "    yi2 = min(box1[1] + box1[3], box2[1] + box2[3])  \n", "    inter_area = max(xi2 - xi1, 0) * max(yi2 - yi1, 0)  \n", "\n", "    if inter_area==0:\n", "        return True\n", "    \n", "    box1_area = box1[2] * box1[3]\n", "    box2_area = box2[2] * box2[3]\n", "    # union_area = box1_area + box2_area - inter_area  \n", "      \n", "    # iou = inter_area / union_area  \n", "    threshold = box2_area * 0.3\n", "    # threshold = min(box1_area, box2_area) * 0.3\n", "    if inter_area < threshold:\n", "        return True\n", "    \n", "    return False\n", "  \n", "def select_boxes_for_occlusion(instance, img_anno):\n", "    flag = True\n", "    for _box in img_anno:\n", "        occlusion = compute_occlusion(instance['bbox'], _box['bbox'])\n", "        if not occlusion:\n", "            flag = False\n", "            break\n", "    return flag\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_iou(box1, box2):\n", "    xi1 = max(box1[0], box2[0])  \n", "    yi1 = max(box1[1], box2[1])  \n", "    xi2 = min(box1[0] + box1[2], box2[0] + box2[2])  \n", "    yi2 = min(box1[1] + box1[3], box2[1] + box2[3])  \n", "    inter_area = max(xi2 - xi1, 0) * max(yi2 - yi1, 0)  \n", "    \n", "    box1_area = box1[2] * box1[3]\n", "    box2_area = box2[2] * box2[3]\n", "    union_area = box1_area + box2_area - inter_area   \n", "    iou = inter_area / union_area  \n", "    return iou\n", "\n", "def center_distance(box1, box2):\n", "    d1 = math.sqrt(box1[2]**2 + box1[3]**2)  \n", "    d2 = math.sqrt(box2[2]**2 + box2[3]**2)  \n", "    length = (d1 + d2) / 2\n", "\n", "    center1_x = box1[0] + box1[2]/2\n", "    center1_y = box1[1] + box1[3]/2\n", "    center2_x = box2[0] + box2[2]/2\n", "    center2_y = box2[1] + box2[3]/2\n", "\n", "    center_d = math.sqrt((center1_x - center2_x)**2 + (center1_y - center2_y)**2)   \n", "    distance = center_d / length\n", "    # if distance < 0.2:\n", "    #     print(1)\n", "    return distance\n", "\n", "def remove_duplicate_boxes(instances):\n", "    removed_ids = []\n", "    jump_list = []\n", "\n", "    for i in range(len(instances)):\n", "        bbox1 = instances[i]['bbox']\n", "        if i in jump_list:\n", "            continue\n", "        for j in range(i + 1, len(instances)):\n", "            if j in jump_list:\n", "                continue\n", "\n", "            bbox2 = instances[j]['bbox']\n", "            iou = calculate_iou(bbox1, bbox2)\n", "            distance = center_distance(bbox1, bbox2)\n", "\n", "            if iou > 0.6 and distance < 0.3:\n", "                i_cls_id = instances[i]['category_id']\n", "                j_cls_id = instances[j]['category_id']\n", "                if i_cls_id != j_cls_id:\n", "                    if iou < 0.75 or distance > 0.2:\n", "                        continue\n", "\n", "                if Frequency_all[i_cls_id] >= Frequency_all[j_cls_id]:\n", "                    removed_ids.append(instances[i]['id'])\n", "                    break\n", "                elif Frequency_all[i_cls_id] < Frequency_all[j_cls_id]:\n", "                    removed_ids.append(instances[j]['id'])\n", "                    jump_list.append(j)\n", "\n", "    return removed_ids"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def instance_filter(img_anno, removed_list):\n", "    new_img_anno=[]\n", "    for anno in img_anno:\n", "        if anno['id'] not in removed_list:\n", "            new_img_anno.append(anno)\n", "    return new_img_anno"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tqdm.notebook import tqdm as tqdm_notebook\n", "import math\n", "from ovtr.util.list_LVIS import Frequency_all\n", "removed_list = []\n", "for i, img in tqdm_notebook(enumerate(data['images'])):\n", "    img_name = img['id']\n", "    if img_name not in coco.imgs:\n", "        continue\n", "    img_anno = coco.img_ann_map[img_name]\n", "    removed_ids = remove_duplicate_boxes(img_anno)\n", "    removed_list += removed_ids\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["annotations = data['annotations']\n", "for i, img in tqdm_notebook(enumerate(data['images'])):\n", "    img_name = img['id']\n", "    if img_name not in coco.imgs:\n", "        continue\n", "    img_anno = coco.img_ann_map[img_name]\n", "    img_anno = instance_filter(img_anno, removed_list)\n", "    for instance in img_anno: # need update\n", "        occlusion = select_boxes_for_occlusion(instance, img_anno)\n", "        annotations[instance['instance_id']].update({'clear':occlusion})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(removed_list)\n", "new_annotations = [anno for anno in annotations if anno['id'] not in removed_list]\n", "data['annotations'] = new_annotations\n", "\n", "save_path = './data/lvis_clear_75_60.json'\n", "with open(save_path, 'w') as f:\n", "    json.dump(data, f)\n", "print('complete')"]}], "metadata": {"kernelspec": {"display_name": "ground", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}}, "nbformat": 4, "nbformat_minor": 2}