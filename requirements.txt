# Strictly configure according to the required version.
addict==2.4.0
Cython==0.29.33
fvcore==0.1.5.post20221221
h5py==3.10.0
ipdb==0.13.13
lvis==0.5.3
matplotlib==3.7.2
mmcv_full==1.3.17
mmdet==2.23.0
motmetrics==1.4.0
numpy==1.22.4
opencv_python==********
pandas==2.0.3
Pillow==9.0.1
PyYAML==6.0.1
scipy==1.11.1
seaborn==0.12.2
setuptools==68.1.0
termcolor==2.3.0
timm==0.9.2
tqdm==4.66.2
transformers==4.31.0
yapf==0.40.1

# Configure the environment to ensure it runs successfully (note: this may lead to changes in evaluation results).
# addict
# Cython
# fvcore
# h5py
# ipdb
# lvis
# matplotlib
# mmcv_full==1.3
# mmdet==2.23
# motmetrics
# numpy==1.22
# opencv_python
# pandas
# Pillow
# PyYAML
# scipy
# seaborn
# setuptools
# termcolor
# timm
# tqdm
# transformers
# yapf
